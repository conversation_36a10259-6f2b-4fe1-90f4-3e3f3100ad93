.form-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    // padding-top: 1rem;
  
    .full-width {
      // Start a column 2 and be 3 columns wide
      grid-column: 1 / span 2;
    }
  
    // Stop form field's validation growing/shinking the dialog
    // & > div {
    //   min-height: 72px;
    // }
  }
  
  .pad-md {
    padding: 7px;
  }
  
  .mg-top-md {
    margin-top: 15px;
  }
  
  .flex {
    display: flex;
  }
  
  .space-between {
    justify-content: space-between;
  }
  
  .mg-btm-50 {
    margin-bottom: 50px;
  }
  
  .delete-form {
      display: flex;
      flex-direction: column;    
      align-items: center;
      row-gap: 1rem;
      >button { // delete
          width: 50%
      }
  }
  .delete-form-continue-button-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 5px 0px;
  
      >button { // continue
          width: 50%;
      }
  }