import React from 'react';
import { WidgetGridCardContext } from './WidgetGridCardContext';
import { ExportableWidget, WidgetDto } from 'redi-types';
import WidgetStorageStoreProvider from '../WidgetStorageStore/WidgetStorageStoreProvider';

function WidgetGridCardProvider(props: Props) {

    const { children, ...other } = props;
    return (
        <WidgetGridCardContext.Provider value={other}>
            <WidgetStorageStoreProvider id={other.widget.id}> {/** Eventually, remove store provider and just move state here */}
                {children}
            </WidgetStorageStoreProvider>
        </WidgetGridCardContext.Provider>
    );
}

export default WidgetGridCardProvider;

export interface WidgetGridCardState {
    dashboardId: string;
    isViewing: boolean;
    widget: WidgetDto;
    editMode: boolean;
    disableResize: boolean;
    isMobile: boolean;
    onSetWidgetEdit: (dto: WidgetDto) => void;
    registerWidget: (widget: ExportableWidget) => void;
    deregisterWidget: (widgetId: string) => void;
}

interface Props extends WidgetGridCardState {
    children: React.ReactNode;
}