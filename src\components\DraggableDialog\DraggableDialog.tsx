import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Dialog, DialogContent, DialogTitle, Paper, PaperProps } from '@mui/material';
import React from 'react';
import Draggable from 'react-draggable';
import './styles.scss';

// Empty ref must be passed in to fix error in 'react-draggable': 'finddomnode-is-deprecated-in-strictmode'
// https://stackoverflow.com/questions/63603902/finddomnode-is-deprecated-in-strictmode-finddomnode-was-passed-an-instance-of-d
function PaperComponent(props: PaperProps) {
  const nodeRef = React.useRef(null);
  return (
    <Draggable
      nodeRef={nodeRef}
      handle="#draggable-dialog-title"
      cancel={'[class*="MuiDialogContent-root"]'}
    >
      <Paper ref={nodeRef} {...props} />
    </Draggable>
  );
}

interface Props {
  title: string;
  isOpen: boolean;
  maxWidth?: 'xs'|'sm'|'md'|'lg'|'xl';
  onCancel: () => void;
  children?: React.ReactNode;
}

function DraggableDialog(props: Props) {

  const handleCancel = () => props.onCancel();
  
  return (
    <>
      <Dialog
        open={props.isOpen}
        onClose={handleCancel}
        PaperComponent={PaperComponent}
        aria-labelledby="draggable-dialog-title"
        fullWidth
        maxWidth={props.maxWidth ?? 'md'}
      >
        <DialogTitle style={{ cursor: 'move' }} id="draggable-dialog-title">
          <div styleName="row">
            <div styleName="header">{ props.title }</div>
            <FontAwesomeIcon styleName="close-icon" icon="close" onClick={handleCancel}/>
          </div>          
        </DialogTitle>
        <div styleName="custom-dialog-content">
          { 
            props.children
            ?? 
            <DialogContent>
              No Dialog Content Set
            </DialogContent>
          }
        </div>
      </Dialog>
    </>
  );
}

export default DraggableDialog;