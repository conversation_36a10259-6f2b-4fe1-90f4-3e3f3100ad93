import { string } from "yargs";

declare module "redi-types" {
  export interface BasePersonCDto {
    personId?: string;
    firstName?: string;
    familyName?: string;
    fullName?: string;
    dateOfBirth?: string;
    yearOfBirth?: number;
    age?: number;
    prefix?: string;
    suffix?: string;
    mergedIntoPersonId?: string;
    isMerged?: boolean;
    gender?: string;
  }

  export interface GetPersonCDto extends BasePersonCDto {
    partyId?: string;
  }

  export interface GetListPersonCDto extends BasePersonCDto {
    partyId: string;
    statusCode: string;
    partyType: string;
    name: string;
    avatarImageUrl: string;
    avatarImageUrl: string;
    userId: string;
    primaryEmail?: string;
    primaryPhone?: string;
  }

  export interface PartyPersonListCDto extends ListPartyCDto {
    person: PersonCDto;
    orgName?: string;
    siteCount?: number;
    jobCount?: number;
    jobTotal?: number;
    latestJob?: Date
  }

  export interface PartyOrganisationListCDto extends ListPartyCDto {
    organisation: GetOrganisationCDto;
  }

  export interface FinalOrgList {
    listOfOrgs: List<PartyOrganisationListCDto>;
    totalNumOfRows: number;
  }

  export interface FinalPersList {
    listOfPers: List<PartyPersonListCDto>;
    totalNumOfRows: number;
  }
}