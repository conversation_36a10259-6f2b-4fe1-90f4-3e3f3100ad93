import { string } from "yup";

declare module "redi-types" {
  export interface BaseAttributeCDto {
    attributeCode: string;
    label: string;
    attributeGroupCode: string;
    attributeValueTypeCode: string;
    inputTypeCode: string;
    description: string;
    sortOrder: number;
    isEnabled: boolean;
    isManyAllowed: boolean;
    visibilityId?: number;
  }
  export interface GetAttributeCDto extends BaseAttributeCDto {
    options?: GetListAttributeDropdownValueCDto[];
  }
  export interface GetListAttributeCDto extends BaseAttributeCDto {
    options?: GetListAttributeDropdownValueCDto[];
  }
  export interface GetListAttributeWithValuesCDto extends BaseAttributeCDto {
    options?: GetListAttributeDropdownValueCDto[];
    values: GetListPartyAttributeCDto[];
  }
}
