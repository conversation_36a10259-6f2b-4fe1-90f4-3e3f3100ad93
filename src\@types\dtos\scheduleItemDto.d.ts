declare module "redi-types" {
    export interface ScheduleItemDto extends DtoBase {
        scheduleItemId: string;
        name: string;
        description: string;
        startTime: string;
        endTime: string;
        parentEntityId: string;
        parentEntityType: string;
        parentEntityDescription: string;
    }

    export interface GetScheduleItemDto extends ScheduleItemDto { }
    
    export interface GetListScheduleItemDto extends ScheduleItemDto { }
}