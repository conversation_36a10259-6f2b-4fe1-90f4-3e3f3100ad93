import React from 'react';
import { useRootPartyForTenant } from '../hooks/useRootPartyForTenant';

/**
 * Example component demonstrating the use of the useRootPartyForTenant hook
 */
const RootPartyExample: React.FC = () => {
  // Use the hook with default parameters
  const { rootParty, isLoading, error } = useRootPartyForTenant(
    true, // Get party attributes
    undefined, // Default attribute group codes
    undefined, // Default parent relation fields
    undefined, // Default child relation fields
    undefined, // Default attribute fields
    'email_1,phone_1', // Contact method fields
    'address_1' // Address fields
  );

  if (isLoading) {
    return <div>Loading root party...</div>;
  }

  if (error) {
    return <div>Error loading root party: {error.message}</div>;
  }

  if (!rootParty) {
    return <div>No root party found for this tenant.</div>;
  }

  return (
    <div>
      <h2>Root Party for Tenant</h2>
      <div>
        <strong>Party ID:</strong> {rootParty.partyId}
      </div>
      <div>
        <strong>Display Name:</strong> {rootParty.displayName}
      </div>
      <div>
        <strong>Status:</strong> {rootParty.statusCode}
      </div>
      <div>
        <strong>Party Type:</strong> {rootParty.partyType}
      </div>
      {rootParty.contactFields && (
        <>
          <h3>Contact Information</h3>
          {rootParty.contactFields.email_1 && (
            <div>
              <strong>Email:</strong> {rootParty.contactFields.email_1}
            </div>
          )}
          {rootParty.contactFields.phone_1 && (
            <div>
              <strong>Phone:</strong> {rootParty.contactFields.phone_1}
            </div>
          )}
        </>
      )}
      {rootParty.address_1 && (
        <>
          <h3>Address</h3>
          <div>
            {rootParty.address_1.lines && <div>{rootParty.address_1.lines}</div>}
            {rootParty.address_1.city && <div>{rootParty.address_1.city}</div>}
            {rootParty.address_1.stateOrProvince && <span>{rootParty.address_1.stateOrProvince}, </span>}
            {rootParty.address_1.postalCode && <span>{rootParty.address_1.postalCode}</span>}
            {rootParty.address_1.countryCode && <div>{rootParty.address_1.countryCode}</div>}
          </div>
        </>
      )}
    </div>
  );
};

export default RootPartyExample;
