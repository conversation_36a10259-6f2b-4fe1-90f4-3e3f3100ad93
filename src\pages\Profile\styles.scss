.form-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    // padding-top: 1rem;
  
    .full-width {
      // Start a column 2 and be 3 columns wide
      grid-column: 1 / span 2;
    }
  
    // Stop form field's validation growing/shinking the dialog
    // & > div {
    //   min-height: 72px;
    // }
}

.pad-md {
  padding: 7px;
}

.mg-top-md {
  margin-top: 15px;
}

.flex {
  display: flex;
}

.space-between {
  justify-content: space-between;
}

.mg-btm-50 {
  margin-bottom: 50px;
}

.fixed-container-offset {
  overflow: auto;
  margin-bottom:70px;
}

.notification-toggle-container {
    padding-top: 20px;
    margin-top: 40px;
}

.notification-toggle.one-line {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.notification-toggle.multi-line {
  display: flex;
  flex-direction: column;
  align-items: baseline;
}

.change-password-btn {
    align-content: flex-end;
    >button {
        margin-bottom: 10px;
    }
}

form {
    button[type="submit"] {
        width: 128px;
    }
}

.caller-code-input {
  flex: 0 75px;
  >div>div {
    border-radius: 4px 0px 0px 4px;
  }
}

.phone-input {
  flex: 1 auto;
  >div>div {
    border-radius: 0px 4px 4px 0px;
  }
  fieldset {
    border-left: none;
  }
}