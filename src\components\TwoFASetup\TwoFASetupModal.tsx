import { Dialog, DialogContent } from "@mui/material";
import withAsyncLoad from "../HOC/withAsyncLoad";

const TwoFASetupForm = withAsyncLoad<{ onClose?: () => void }>(() => import("logincomponents/TwoFASetupForm"));

function TwoFASetupModal(props: Props) {

  const { open, onClose } = props;

    return (
      <Dialog
        open={open}
        keepMounted
        onClose={onClose}
      >
        <DialogContent>
            {open ? <TwoFASetupForm onClose={onClose} /> : null}
        </DialogContent>
      </Dialog>
    );
}

export default TwoFASetupModal;

interface Props {
  open: boolean;
  onClose: () => void;
}