declare module "redi-types" {
    export interface BaseOrganisationCDto extends DtoBase {
        organisationId: string;
        name: string;
        organisationReference?: string;
        description?: string;
        organisationTypeCode: string;
        organisationTypeDescription?: string;
        isRoot?: boolean;
    }

    export interface GetOrganisationCDto extends BaseOrganisationCDto {
        partyId: string;
        partyType?: string;
        statusCode?: string;
        avatarImageId?: string;
        avatarImageUrl?: string;
        userId?: string;
        addresses?: GetListAddressCDto[];
        primaryEmail?: string;
        primaryPhone?: string;
        primaryAddress?: string;
        contactMethods?: GetListContactMethodCDto[];
    }

    export interface GetListOrganisationCDto extends BaseOrganisationCDto {
        partyId: string;
        partyType: string;
        statusCode: string;
        avatarImageId?: string;
        avatarImageUrl?: string;
        userId?: string;
        addresses?: GetListAddressCDto[];
        primaryEmail?: string;
        primaryPhone?: string;
        primaryAddress?: string;
        contactMethods?: GetListContactMethodCDto[];
    }
}