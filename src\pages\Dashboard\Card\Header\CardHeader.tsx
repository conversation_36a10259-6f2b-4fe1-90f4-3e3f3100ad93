import { WidgetDto } from "redi-types";
import FontAwesomeIconStyled from "../../../../components/MuiStyleComponents/FontAwesomeIconStyled";
import { CardHeader as Mui<PERSON>ardHeader, IconButton, Grid } from "@mui/material";
import CardDateRangePicker from "./DateRangePicker/CardDateRangePicker";
import GenerateAISummaryButton from "./GenerateAISummaryButton/GenerateAISummaryButton";
import { type PollingMeta } from "querydatacomponents/useDataPolling";

function EditIconButton({ show, widget, onSetWidgetEdit }: { show: boolean, widget: WidgetDto, onSetWidgetEdit: (dto: WidgetDto) => void }) {

    const handleClick = (event?: React.MouseEvent) => {
        handleStopPropagation(event);
        onSetWidgetEdit(widget);
    };

    const handleStopPropagation = (event?: React.MouseEvent | React.TouchEvent) => {
        event?.stopPropagation();
    };

    return (
        show ?
        <IconButton
            aria-label="edit"
            onClick={handleClick}
            onMouseDown={handleStopPropagation}
            onTouchStart={handleStopPropagation}
            sx={{
                zIndex: 99,
                position: 'absolute',
                right: 2,
                top: 2
            }}
        >
                <FontAwesomeIconStyled fontSize='1.25rem' icon={["fad", "edit"]}/>
        </IconButton> : null
    );
}


function CardHeader({ title, widget, isMobile, editMode, onSetWidgetEdit, polledWidgetData }: { title: React.ReactNode, widget: WidgetDto, isMobile: boolean, editMode: boolean, onSetWidgetEdit: (dto: WidgetDto) => void, polledWidgetData?: PollingMeta  }) {
    return (
        <>
            <Grid container justifyContent={"space-between"}>
                <Grid item xs><MuiCardHeader titleTypographyProps={{fontSize: '1rem'}} title={title} /></Grid>
                <Grid item xs alignSelf={"center"} textAlign={"end"}>
                  <GenerateAISummaryButton widget={widget} polledWidgetData={polledWidgetData}/>
                  <CardDateRangePicker />
                </Grid>
            </Grid>
            <EditIconButton show={editMode} widget={widget} onSetWidgetEdit={onSetWidgetEdit} />
        </>
    );
}

export default CardHeader;
