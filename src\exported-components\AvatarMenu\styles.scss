@import "../../config/theme/vars.scss";

* {
  font: $font;
}

.mobile-only {
  @media ($gtsm) {
    display: none;
  }
}

.mobile-menu-container {

  margin-left: 2rem;
  margin-right: 2rem;

  svg {
    transition: color 0.2s ease;

    &:hover {
      color: black;
    }
  }

  .mobile-menu-button {
    @media ($gtsm) {
      display: none;
    }
  }


  .full-width {
    display: flex;
    align-items: center;
    flex-grow: 1;
    // margin-left: 0.2rem;

    .company-logo {
      height: 30px;
      margin-right: 0.2rem;
    }

    .label {
      font-size: 1.4rem;
      white-space: nowrap;
    }
  }
}

.justify-center {
  justify-content: center;
}

.app-bar-container {
  position: relative;
  // left: -2.5%;
}

.row {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.username {
  font-size: 0.9rem;
  white-space: nowrap;
  font-weight: 500;
  // margin-right: 8px;
  margin-left: 10px;
}

.business-name {
  color: #FFFFFF;
  font-size: 0.9rem;
  white-space: nowrap;
  font-weight: 500;
  margin-right: 8px;
}

.row-menu-icon {
  height: 18px;
  width: 18px;
  // color: rgba(0, 0, 0, 0.6);
  color: $primaryColor;
}

.user-menu-button {
  min-width: unset;

  >svg {
    margin-left: 5px;
  }
}

.avatar-menu {
  margin-right: 8px;
}

.logout-icon {
  margin-left: 7px;
}

.mui-menu-top {
  display: flex;
  flex-direction: column;
}

.avatar {
  margin-left: 15px;

}

.email,.company-name {
  font-size: 0.8rem;
  white-space: nowrap;
  font-weight: 500;
  margin-right: 18px;
  margin-left: 18px;
  margin-top: 7px;
}

.menu-item {
  margin-left: 8px;
  font-size: 0.8rem;
}

.notification-component {
}