declare module "redi-types" {
    export interface ScheduleItemCDto extends DtoBase {
        scheduleItemId: string;
        scheduleItemTypeCode: string;
        name: string;
        description: string;
        startTime: string;
        endTime: string;
        parentEntityId: string;
        parentEntityType: string;
        parentEntityDescription: string;
    }

    export interface GetScheduleItemCDto extends ScheduleItemCDto { }
    
    export interface GetListScheduleItemCDto extends ScheduleItemCDto { }
}