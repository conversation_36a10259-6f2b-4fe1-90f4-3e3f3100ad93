import { FormControl, FormHelperText, InputBaseProps, InputLabel, MenuItem, Select } from "@mui/material";
import { useEffect, useState } from "react";
import RoleTypeService from "../../services/roleType";
import { RoleTypeCDto } from "redi-types";
import { ErrorMessage } from "formik";

function PartyRoleTypeSelect(props: Props) {
    const { initialValue = "", variant, id, name, label = "Position", size, error, placeholder, onBlur, onChange } = props;
    const [ roleTypes, setRoleTypes ] = useState<RoleTypeCDto[]>([]);
    useEffect(() => {
        RoleTypeService.getList().then((data) => {
            if (!data.error && data.data) {
                setRoleTypes(data.data);
            }
        })
    }, []);

    return (
        <>
            <FormControl fullWidth>
                <InputLabel id={id}>{label}</InputLabel>
                <Select
                    fullWidth
                    variant={variant}
                    name={name}
                    label={label}
                    labelId={id}
                    placeholder={placeholder}
                    value={initialValue}
                    onChange={(event) => {
                        const value = event.target.value;
                        onChange(value);
                    }}
                    onBlur={onBlur}
                    error={error}
                    size={size}
                >
                    {roleTypes.map((item, index) => (
                        <MenuItem key={`_role_type_${index}`} value={item.roleTypeCode}>
                            {item.label}
                        </MenuItem>
                    ))}
                </Select>
                {error ? <FormHelperText error={true}><ErrorMessage name={name} /></FormHelperText> : null}
            </FormControl>
        </>
    );
}

export default PartyRoleTypeSelect;

interface Props {
    id: string;
    name: string;
    placeholder?: string;
    label?: string;
    initialValue?: string;
    error?: boolean;
    helperText?: React.ReactNode;
    size?: "small" | "medium" | undefined;
    variant?: "standard" | "outlined" | "filled" | undefined;
    onBlur?: InputBaseProps['onBlur'];
    onChange: (val: string) => void;
}