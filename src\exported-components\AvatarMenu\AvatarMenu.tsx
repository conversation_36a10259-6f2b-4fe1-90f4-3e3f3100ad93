import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>u<PERSON><PERSON> as MuiMenu<PERSON><PERSON>, Divider, Typo<PERSON>, useTheme, FormControlLabel, Switch, debounce } from "@mui/material";
import { useEffect, useMemo, useState } from "react";
import './styles.scss';
import { useSecurityStore } from "redi-security-components";
import { useNavigate } from "react-router-dom";
import { ExtendedPartyCDto, PartyCDto } from "redi-types";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import Notifications from "commoncomponents/Notifications";
import TopMenuBell from "commoncomponents/TopMenuBell";
import { shallow } from 'zustand/shallow';
import MyPartyService from "../../services/myParty";
import FontAwesomeIconStyled from "../../components/MuiStyleComponents/FontAwesomeIconStyled";
import useUpdateMyParty from "../../hooks/useUpdateMyParty";

const AvatarMenu = (props: Props) => {
  const { currentTheme, onSetTheme } = props;
  const { user, setLoggedIn } = useSecurityStore((state => ({ user: state.user, setLoggedIn: state.setLoggedIn })), shallow);
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const isOpen = Boolean(anchorEl);
  const [party, setParty] = useState<ExtendedPartyCDto>();
  const { updateMyParty } = useUpdateMyParty();

  const debounceUpdateParty = useMemo(() => 
    debounce((party: ExtendedPartyCDto, theme: string) => updateMyParty({ data: {...party, fields: {...party.fields, siteTheme: theme }}, clearFields: {}}), 300)
  , []);

  //Get the availability status party attribute
  useEffect(() => {
    console.log(user);
    if (user && user.partyId) {
      fetchParty(user?.partyId);
    }
  }, [user]);


  const fetchParty = async (partyId: string) => {
    try {
      const party = await MyPartyService.get(true, "EmployeeProfile");
      if (!party.error && party.data) {
        const theme = party.data.fields?.siteTheme;
        theme && onSetTheme && onSetTheme(theme);
        setParty(party.data);
      }
    } catch (error) {}
  };

  const openMenu = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };
  const handleClose = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    setAnchorEl(null);
  };

  const navigateToProfile = (event: React.MouseEvent) => {
    event.stopPropagation();
    setAnchorEl(null);
    navigate('/settings/profile');
  };

  const logout = async (event: React.MouseEvent) => {
    event.stopPropagation();
    setAnchorEl(null);
    setLoggedIn(false);
  };

  // notifications component
  const [notificationOpen, setNotificationOpen] = useState<boolean>(false);

  const handleOpenCloseNotifications = () => {
    setNotificationOpen(!notificationOpen);
  }

  const handleChangeTheme = (event: React.ChangeEvent<HTMLInputElement>) => {
    const theme = event.target.checked ? 'dark' : 'light';
    //Fire and forget my party update
    if (party) {
      //debounce so we don't spam the server
      debounceUpdateParty(party, theme);
    }
    onSetTheme && onSetTheme(theme);
  };

  return (
    <div styleName="row">
      <div styleName="row">
        {party?.organisation && party?.organisation.name &&
          <>
            <div styleName="business-name">{party?.organisation.name}</div>
            <Button
              aria-controls={isOpen ? 'basic-menu' : undefined}
              aria-haspopup="true"
              aria-expanded={isOpen ? 'true' : undefined}
              color="primary"
              // onClick={openMenu}
              styleName="user-menu-button"
            >
              <FontAwesomeIcon color="white" icon={['fad', 'caret-down']} />
            </Button>
          </>
        }
      </div>

      <TopMenuBell handleOpenCloseNotifications={handleOpenCloseNotifications} />

      <Button
        aria-controls={isOpen ? 'basic-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={isOpen ? 'true' : undefined}
        color="primary"
        onClick={openMenu}
        styleName="user-menu-button"
      >
        <Avatar sx={{ width: 45, height: 45, border: '3px solid #883ffa' }} src={party?.avatarImageUrl} />
        <FontAwesomeIcon color="white" icon={['fad', 'caret-down']} />
      </Button>

      <MuiMenu
        id="actions-menu"
        anchorEl={anchorEl}
        open={isOpen}
        sx={{ marginTop: '8px' }}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        MenuListProps={{ 'aria-labelledby': 'menu-button' }}
      >
        <div styleName="mui-menu-top">
          <div styleName="row">
            <Avatar sx={{ width: 45, height: 45, border: '3px solid #883ffa' }} styleName="avatar" src={party?.avatarImageUrl} />
            <Typography styleName="username">{user?.firstName} {user?.lastName}</Typography>
          </div>
          <Typography styleName="email">{user?.email.toLocaleLowerCase()}</Typography>
          <Typography styleName="company-name">{party?.rootOrganisationDisplayName}</Typography>
        </div>

        <Divider style={{marginBottom: "7px", marginTop: "7px"}} />

        <div>
          {onSetTheme ?
          <FormControlLabel
            labelPlacement="start"
            control={<Switch checked={currentTheme === 'dark'} onChange={handleChangeTheme} />}
            label="Theme Mode"
          /> : null}
          <MuiMenuItem onClick={navigateToProfile}>
            <FontAwesomeIconStyled icon={['fad', 'user']} />
            <ListItemText disableTypography styleName="menu-item">Profile</ListItemText>
          </MuiMenuItem>
          <MuiMenuItem onClick={logout}>
            <FontAwesomeIconStyled icon={['fad', 'arrow-right-from-bracket']} />
            <ListItemText disableTypography styleName="menu-item">Logout</ListItemText>
          </MuiMenuItem>
        </div>
      </MuiMenu>

      <div styleName="notification-component">
      {
        notificationOpen && <Notifications open={true} onClose={handleOpenCloseNotifications} />
      }
      </div>
    </div>
  );
}

export default AvatarMenu;

interface Props {
  onSetTheme?: (theme: string) => void;
  currentTheme?: string;
}