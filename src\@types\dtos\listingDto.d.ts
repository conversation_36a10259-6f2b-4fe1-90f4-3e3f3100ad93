declare module "redi-types" {
  export interface ListingListDto extends DtoBase {
      listingId: string;
      subject?: string;
      statusCode?: string;
      profileImageId: string;
      profileImageUrl?: string;
      parentEntityId: string;
      parentEntityType?: string;
      fromDate: string;
      toDate: string;
      description?: string;
  }

  export interface GetListingDto extends ListingListDto {
      listingImages?: ListingImageDto[];
      listingAttributes?: ListingAttributeDto[];
  }
}