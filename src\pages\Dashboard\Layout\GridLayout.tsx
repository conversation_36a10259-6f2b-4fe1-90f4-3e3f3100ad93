import { Layout, Responsive, WidthProvider } from "react-grid-layout";
import { BreakpointSize, DashboardDto, ExportableWidget, LayoutDto, WidgetDto } from "redi-types";
import GridCard from "../Card/GridCard";
import { memo, useEffect, useRef, useState } from "react";
import { Box, Button, Card, Typography, useMediaQuery } from "@mui/material";
import { useNavigate } from "react-router-dom";
import { PageContext } from "../../../enum/pageContextEnum";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { BreakPoint } from "../../../constants/Breakpoints";
import WidgetSetupOverlay from "../../../components/OnboardingOverlay/WidgetSetupOverlay";

const ResponsiveReactGridLayout = WidthProvider(Responsive);

function WidgetUnavailable ({dashboard, editMode}: {dashboard: DashboardDto, editMode: boolean}) {

    const navigate = useNavigate();

    const handleAddWidgetClick = () => {
        navigate("/integrations", { state: { context: PageContext.AddDashboardWidget, referenceNo: dashboard.referenceNo, subject: dashboard.subject } });
    };

    return (
        <Box
            display={"flex"}
            justifyContent={"center"}
            alignContent={"center"}
            width={"100%"}
            height={"100%"}
        >
            { editMode ? 
            <>
            <Box display="flex" flexDirection="column">
                <Typography variant="body1" component="div">You do not currently have any widgets on this dashboard</Typography>
                <br /><br />
                <Button onClick={handleAddWidgetClick} color="primary" variant="contained">
                    Add Widget<FontAwesomeIcon icon={"plus"} />
                </Button>
            </Box>
            <WidgetSetupOverlay onClick={handleAddWidgetClick} />
            </>: 
            <Typography variant="body1" component="div" textAlign="center">Be the first to add a Widget</Typography> }
        </Box>
    );
}

function GridLayout(props: Props) {

    const { dashboard, isViewing, editMode, layout, breakpoint, noHeader, setBreakpoint, setLayouts, onSetWidgetEdit, registerWidget, deregisterWidget } = props;
    const timeoutRef = useRef<NodeJS.Timeout | null>();
    const rootRef = useRef<HTMLDivElement>(null);
    // Disable the move and resize controls on small screens
    const [ disableControls, setDisableControls ] = useState(false);
    const isMobile = disableControls;

    useEffect(() => {
        getCurrentBreakpoint();
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
                timeoutRef.current = null;
            }
        };
    }, []);

    const getCurrentBreakpoint = () => {
        if (rootRef.current && !breakpoint) {
            for (let field in BreakPoint) {
                let breakpoint = field as BreakpointSize;
                if (rootRef.current.clientWidth >= BreakPoint[breakpoint]) {
                    setBreakpoint(breakpoint);
                    return;
                }
            }
        }
	};

    const handleLayoutChange = (layout: Layout[]) => {
        if (!editMode) { return; }
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
            timeoutRef.current = null;
        }
        timeoutRef.current = setTimeout(() => {
            setLayouts(layout);
        }, 300);
    };

    const handleBreakPointChange = (breakpoint: BreakpointSize) => {
        if (breakpoint === "sm" || breakpoint === "xs" || breakpoint === "xxs") {
            setDisableControls(true);
        } else {
            setDisableControls(false);
        }
        setBreakpoint(breakpoint);
    };

    return (
        <Card 
            className="dashboard-container-scroll"
            style={{ 
            top: noHeader ? "0px" : "77px",//If you add back the Dashboard datepicker in DashboardHeader.tsx, update 77px to 100px.
            position: "absolute",
            width: "100%",
            left: 0,
            overflow: "auto",
            borderRadius: 0,
            paddingTop: noHeader ? "25px" : undefined, //If you add back the Dashboard datepicker in DashboardHeader.tsx, update 77px to 100px.
            height: noHeader ? "100%" : "calc(100% - 77px)", visibility: isViewing ? "visible" : "hidden"}} ref={rootRef}>
            {layout && dashboard && dashboard?.config?.widgets?.length > 0 ?
            <ResponsiveReactGridLayout 
                className="layout" 
                style={{paddingBottom: "15px"}}
                margin={isMobile ? [0,4] : [25, 25]} //Spacing between cards
                containerPadding={isMobile ? [2,0] : [30, 0]} //Grid container padding
                breakpoints={{lg: layout.breakpoints.sm, xxs: layout.breakpoints.xxs}}
                cols={{lg: layout.columns.lg, xxs: layout.columns.xxs}}
                //rowHeight={(breakpoint === 'md' || breakpoint === 'xxs') ? undefined : 10} 
                rowHeight={isMobile ? 33 : 10}
                layouts={{lg: layout.layouts.lg, xxs: layout.layouts.xxs}}
                measureBeforeMount={false}
                isDraggable={editMode && !disableControls}
                isResizable={editMode && !disableControls}
                onDragStop={handleLayoutChange}
                onResizeStop={handleLayoutChange}
                onBreakpointChange={handleBreakPointChange}
            >
            {dashboard.config.widgets.map((item, i) => (
                <div key={item.id}>
                    <GridCard dashboardId={dashboard.referenceNo!} isMobile={isMobile} editMode={editMode} key={item.id} widget={item} isViewing={isViewing} disableResize={disableControls} onSetWidgetEdit={onSetWidgetEdit} registerWidget={registerWidget} deregisterWidget={deregisterWidget}/>
                </div>
            ))}
            </ResponsiveReactGridLayout> : <WidgetUnavailable editMode={editMode} dashboard={dashboard} />}
        </Card>
    );
}

export default memo(GridLayout, ((prevProps, nextProps) => {
    if (nextProps.isViewing) {
        //only-re-render the viewed dashboard
        return prevProps.dashboard === nextProps.dashboard &&
            prevProps.editMode === nextProps.editMode &&
            prevProps.layout === nextProps.layout &&
            prevProps.isSaving === nextProps.isSaving &&
            nextProps.isViewing === prevProps.isViewing
    }
    return nextProps.isViewing === prevProps.isViewing; //true - no-rerender  false - render
}));

interface Props {
    isViewing: boolean;
    isSaving: boolean;
    dashboard: DashboardDto;
    editMode: boolean;
    layout: LayoutDto;
    breakpoint: BreakpointSize;
    noHeader: boolean;
    setBreakpoint: (breakpoint: BreakpointSize) => void;
    setLayouts: (layout: Layout[]) => void;
    onSetWidgetEdit: (dto: WidgetDto) => void;
    registerWidget: (widget: ExportableWidget) => void;
    deregisterWidget: (widgetId: string) => void;
}