import { Field, FormikErrors, FormikTouched, getIn, FieldArray, FieldArrayRenderProps } from "formik";
import { AddressTypeCDto, GetListAddressCDto } from "redi-types";
import { NIL as NIL_UUID, v4 as uuidv4 } from 'uuid';
import './styles.scss';
import { MenuItem, Radio, TextField, Tooltip } from "@mui/material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { IsNullOrUndefined } from "../../utils/isNullOrUndefined";
import AddressTypeService from "../../services/addressType";
import { useEffect, useState } from "react";
import { PartyTypeEnum } from "../../enum/PartyTypeEnum";

function addAddress(fields: FieldArrayRenderProps, partType: PartyTypeEnum, addressCount: number) {
    const dto = {
        addressId: NIL_UUID,
        isPreferredAddress: false,
        parentEntityId: NIL_UUID,
        parentEntityType: partType,
        sortOrder: addressCount + 1,
        lines: "",
        city: "",
        stateOrProvince: "",
        postalCode: "",
        countryCode: "",
        addressTypeCode: "",
        fullAddress: ""
    };
    fields.push(dto);
}

function PartyAddress(props: Props) {
    const [addressTypeList, setAddressTypeList] = useState<AddressTypeCDto[]>([]);
    const { touchedList, errorList, addresses, showPreferredAddress, partType, setFieldValue } = props;

    useEffect(() => {
        try {
            AddressTypeService.getList().then((data) => {
                if (!data.error && data.data) {
                    setAddressTypeList(data.data);
                }
            });
        } catch (error) {}
    }, [])


    return (
        <FieldArray name="addresses">
            {(fields: FieldArrayRenderProps) => (
                <div styleName="column card-fields-container">
                    <div styleName="column">
                        {
                            addresses &&
                            addresses.map((dto, index) => (
                                <div styleName="row address-row-container" key={`address_${dto.addressId}`}>
                                    <div styleName="column field-container size-5">
                                        <div styleName="row address-field-gap">
                                            <div styleName="field-container size-3">
                                                <Field
                                                    id={`addresses_${index}_lines`}
                                                    name={`addresses[${index}].lines`}
                                                    label={"Address Lines"}
                                                    as={TextField}
                                                    error={touchedList && touchedList[index]?.lines && Boolean(getIn(errorList?.[index], `lines`))}
                                                    helperText={touchedList && touchedList[index]?.lines && getIn(errorList?.[index], `lines`)}
                                                />
                                            </div>
                                        </div>
                                        <div styleName="row address-field-gap">
                                            <div styleName="field-container size-1">
                                                <Field
                                                    select
                                                    id={`addresses_${index}_stateOrProvince`}
                                                    name={`addresses[${index}].stateOrProvince`}
                                                    label={"State"}
                                                    as={TextField}
                                                    error={touchedList && touchedList[index]?.stateOrProvince && Boolean(getIn(errorList?.[index], `stateOrProvince`))}
                                                    helperText={touchedList && touchedList[index]?.stateOrProvince && getIn(errorList?.[index], `stateOrProvince`)}
                                                >
                                                    <MenuItem value="NSW">NSW</MenuItem>
                                                    <MenuItem value="QLD">QLD</MenuItem>
                                                    <MenuItem value="SA">SA</MenuItem>
                                                    <MenuItem value="TAS">TAS</MenuItem>
                                                    <MenuItem value="VIC">VIC</MenuItem>
                                                    <MenuItem value="WA">WA</MenuItem>
                                                </Field>
                                            </div>
                                            <div styleName="field-container size-1">
                                                <Field
                                                    id={`addresses_${index}_postalCode`}
                                                    name={`addresses[${index}].postalCode`}
                                                    label={"Postal Code"}
                                                    as={TextField}
                                                    error={touchedList && touchedList[index]?.postalCode && Boolean(getIn(errorList?.[index], `postalCode`))}
                                                    helperText={touchedList && touchedList[index]?.postalCode && getIn(errorList?.[index], `postalCode`)}
                                                />
                                            </div>
                                        </div>
                                        <div styleName="row">
                                            <div styleName="field-container size-1">
                                                {/* //TODO: Get country dropdown from non-existent redi-microfrontend-common country component */}
                                                <Field
                                                    select
                                                    id={`addresses_${index}_countryCode`}
                                                    name={`addresses[${index}].countryCode`}
                                                    label={"Country"}
                                                    as={TextField}
                                                    error={touchedList && touchedList[index]?.countryCode && Boolean(getIn(errorList?.[index], `countryCode`))}
                                                    helperText={touchedList && touchedList[index]?.countryCode && getIn(errorList?.[index], `countryCode`)}
                                                >
                                                    <MenuItem value="AU">Australia</MenuItem>
                                                </Field>
                                            </div>
                                            <div styleName="field-container size-1">
                                                {addressTypeList && addressTypeList.length > 0 ?
                                                    <Field
                                                        select
                                                        id={`addresses_${index}_addressTypeCode`}
                                                        name={`addresses[${index}].addressTypeCode`}
                                                        label={"Type"}
                                                        as={TextField}
                                                        error={touchedList && touchedList[index]?.addressTypeCode && Boolean(getIn(errorList?.[index], `addressTypeCode`))}
                                                        helperText={touchedList && touchedList[index]?.addressTypeCode && getIn(errorList?.[index], `addressTypeCode`)}
                                                    >
                                                        {addressTypeList.map((item, index) => (
                                                            <MenuItem key={item.addressTypeCode} value={item.addressTypeCode}>{item.description}</MenuItem>
                                                        ))}
                                                    </Field> : null}
                                            </div>
                                        </div>
                                    </div>
                                    <div styleName="column field-container size-1 center-justify center-align">
                                        {
                                            showPreferredAddress == true &&
                                            <div styleName="column center-justify center-align">
                                                <div styleName="field-container-title">Preferred</div>
                                                <div>
                                                    <Field
                                                        type="checkbox"
                                                        id={`addresses_${index}`}
                                                        name={`addresses[${index}].isPreferredAddress`}
                                                        onChange={() => {
                                                            let addressValues = addresses;
                                                            if (!IsNullOrUndefined(addressValues)) {
                                                                let all = addressValues || [];
                                                                for (let ii = 0, ilen = all.length; ii < ilen; ii++) {
                                                                    setFieldValue(`addresses[${ii}].isPreferredAddress`, false);
                                                                }
                                                                setFieldValue(`addresses[${index}].isPreferredAddress`, true);
                                                            }
                                                        }}
                                                        as={Radio}
                                                    />
                                                </div>
                                            </div>
                                        }
                                        <Tooltip title="Remove address" placement="bottom">
                                            <div styleName="field-container-action-button center-justify center-align" onClick={() => fields.remove(index)}>
                                                <FontAwesomeIcon icon="trash" />
                                            </div>
                                        </Tooltip>
                                    </div>
                                </div>
                            ))
                        }
                    </div>
                    <div styleName="row center-justify center-align">
                        <Tooltip title="Add a new address" placement="bottom">
                            <div styleName="action-button" onClick={() => addAddress(fields, partType, addresses?.length || 0)}>
                                <FontAwesomeIcon icon="circle-plus" />
                                <div>Add Address</div>
                            </div>
                        </Tooltip>
                    </div>
                </div>
            )}
        </FieldArray>
    )
}

export default PartyAddress;

interface Props {
    touchedList: FormikTouched<GetListAddressCDto>[] | undefined;
    errorList: string | string[] | FormikErrors<GetListAddressCDto>[] | undefined;
    addresses: GetListAddressCDto[] | undefined;
    showPreferredAddress: boolean;
    partType: PartyTypeEnum;
    setFieldValue: (field: string, value: any, shouldValidate?: boolean | undefined) => void;
}