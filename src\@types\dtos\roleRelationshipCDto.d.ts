declare module "redi-types" {
    export interface BaseRoleRelationshipCDto {
        roleRelationshipId: string;
        fromRoleType: string;
        fromRoleTypeLabel: string;
        toRoleType: string;
        toRoleTypeLabel: string;
        partyRelationshipTypeCode: string;
        partyRelationshipTypeLabel: string;
        inversePartyRelationshipTypeCode: string;
        inversePartyRelationshipTypeLabel: string;
        isDefaultForRelationship: boolean;
        isEnabled: boolean;
    }

    export interface GetRoleRelationshipCDto extends BaseRoleRelationshipCDto {}

    export interface GetListRoleRelationshipCDto extends BaseRoleRelationshipCDto {}
}