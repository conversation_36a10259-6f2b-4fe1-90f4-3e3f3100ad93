import { GetListAttributeDropdownValueCDto, ListResponseDto } from "redi-types";
import RediAutocompleteMultiselectField, { AutocompleteMultiselectProps } from "../RediField/AutocompleteMultiselect/RediAutocompleteMultiselectField";
import { HttpPromise, HttpResult } from "redi-http";
import { useEffect, useRef, useState } from "react";
import AttributeDropdownService from "../../services/attributeDropdownValue";

const getIndustrites = (list: GetListAttributeDropdownValueCDto[], value?: string) => {
    //Set Industry
    const industryValues = value?.trimStart()?.split('|') ?? [];
    if (industryValues && industryValues.length > 0) {
        const _industryValues = [];
        for (const value of industryValues) {
            const industry = list.find(x => x.value === value);
            if (industry) {
                _industryValues.push(industry);
            }
        }
        if (_industryValues.length > 0) {
            return _industryValues;
        }
    }
    return [];
};

function IndustryAutocompleteMultiselectField(props: Props) {
    const { value, onChange, ...other } = props;
    const [ isBusy, setIsBusy ] = useState(false);
    const [ list, setList ] = useState<ListResponseDto<GetListAttributeDropdownValueCDto> | null>(null);
    const [ initialValues, setInitialValues] = useState<GetListAttributeDropdownValueCDto[]>([]);
    const promiseRef1 = useRef<HttpPromise<GetListAttributeDropdownValueCDto[]>>();
    const fetch = async () => {
        if (isBusy) { return; }
        setIsBusy(true);
        try {
            promiseRef1.current = AttributeDropdownService.getListForAttribute("Industry");

            const response = await promiseRef1.current;
            if (!response.error && response.data && response.data.length > 0) {
                setInitialValues(getIndustrites(response.data, value));
                setList({ list: response.data, totalNumOfRows: response.data.length });
            }
        } catch (error) {}
        finally {
            setIsBusy(false);
        }
    };

    const clearPromises = () => {
        if (promiseRef1.current?.cancel) {
            promiseRef1.current.cancel();
        }
    };

    useEffect(() => {
        fetch();
        return clearPromises;
    }, []);

    const handleCall = (query?: string) => {
        return new Promise<HttpResult<ListResponseDto<GetListAttributeDropdownValueCDto>>>((resolve, reject) => {
            resolve({ data: list ?? { list: [], totalNumOfRows: 0 } });
        }) as HttpPromise<ListResponseDto<GetListAttributeDropdownValueCDto>>;
    };

    const handleChange = (e: React.ChangeEvent<{}>, list:GetListAttributeDropdownValueCDto[], removedItem?: GetListAttributeDropdownValueCDto) => {
        setInitialValues(list);
        const rawString = list.map(x => x.value).join("|");
        onChange(rawString);
    };
    
    return (
        isBusy ? <span>...Loading</span> :
        list ?
        <RediAutocompleteMultiselectField 
            {...other}
            value={initialValues}
            onChange={handleChange}
            callService={handleCall}
        /> : <span>error</span>
    );
}

interface Props extends Omit<AutocompleteMultiselectProps, "callService" | "onChange" | "value"> {
    value?: string;
    onChange: (val: string) => void;
}

export default IndustryAutocompleteMultiselectField;