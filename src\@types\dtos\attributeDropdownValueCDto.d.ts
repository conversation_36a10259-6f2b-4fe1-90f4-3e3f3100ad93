declare module "redi-types" {
    export interface BaseAttributeDropdownValueCDto  {
        attributeDropdownValueId: number;
        attributeCode: string;
        displayValue: string;
        value: string;
        sortOrder: number;
        isEnabled: boolean
    }

    export interface GetAttributeDropdownValueCDto extends BaseAttributeDropdownValueCDto {}

    export interface GetListAttributeDropdownValueCDto extends BaseAttributeDropdownValueCDto {}
}
