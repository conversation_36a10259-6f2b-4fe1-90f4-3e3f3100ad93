import React, { useRef, useCallback, ReactNode } from 'react';

interface SwipeProps {
    children: ReactNode;
    isEnabled?: boolean;
    onSwipeLeft?: () => void;
    onSwipeRight?: () => void;
    onSwipeUp?: () => void;
    onSwipeDown?: () => void;
    tolerance?: number;
    className?: string;
    style?: React.CSSProperties;
}

interface TouchPosition {
    x: number;
    y: number;
}

const Swipe: React.FC<SwipeProps> = ({
    children,
    onSwipeLeft,
    onSwipeRight,
    onSwipeUp,
    onSwipeDown,
    tolerance = 50,
    isEnabled = true,
    className,
    style
}) => {
    const touchStartRef = useRef<TouchPosition | null>(null);
    const touchEndRef = useRef<TouchPosition | null>(null);

    const handleTouchStart = useCallback((e: React.TouchEvent) => {
        const touch = e.touches[0];
        touchStartRef.current = {
            x: touch.clientX,
            y: touch.clientY
        };
        touchEndRef.current = null;
    }, []);

    const handleTouchMove = useCallback((e: React.TouchEvent) => {
        const touch = e.touches[0];
        touchEndRef.current = {
            x: touch.clientX,
            y: touch.clientY
        };
    }, []);

    const handleTouchEnd = useCallback(() => {
        if (!touchStartRef.current || !touchEndRef.current || !isEnabled) {
            return;
        }

        const deltaX = touchEndRef.current.x - touchStartRef.current.x;
        const deltaY = touchEndRef.current.y - touchStartRef.current.y;

        const absDeltaX = Math.abs(deltaX);
        const absDeltaY = Math.abs(deltaY);

        // Check if the swipe distance meets the tolerance threshold
        if (absDeltaX < tolerance && absDeltaY < tolerance) {
            return;
        }

        // Determine if it's a horizontal or vertical swipe
        if (absDeltaX > absDeltaY) {
            // Horizontal swipe
            if (deltaX > 0 && onSwipeRight) {
                console.log("Swipe Right");
                onSwipeRight();
            } else if (deltaX < 0 && onSwipeLeft) {
                console.log("Swipe Left");
                onSwipeLeft();
            }
        } else {
            // Vertical swipe
            if (deltaY > 0 && onSwipeDown) {
                onSwipeDown();
            } else if (deltaY < 0 && onSwipeUp) {
                onSwipeUp();
            }
        }

        // Reset touch positions
        touchStartRef.current = null;
        touchEndRef.current = null;
    }, [onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown, tolerance, isEnabled]);

    return (
        <div
            className={className}
            style={style}
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
        >
            {children}
        </div>
    );
};

export default Swipe;
