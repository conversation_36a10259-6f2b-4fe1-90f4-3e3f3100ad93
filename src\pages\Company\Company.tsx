import { Box, CircularProgress, FormControlLabel, Grid, MenuItem, Radio, RadioGroup, TextField } from "@mui/material";
import { Field, FieldProps, Formik, FormikHelpers, FormikProps, getIn } from "formik";
import { AddressTypeCDto, CountryCDto, ExtendedPartyCDto, FileCDto, GetListAttributeDropdownValueCDto, RequestCDto, TimezoneCDto } from "redi-types";
import FormHeader from "../../components/Form/Header/FormHeader";
import HeadingInformation from "../../components/FormStyles/HeadingInformation/HeadingInformation";
import { DatePicker } from '@mui/x-date-pickers';
import dayjs from "dayjs";
import LogoEditor from 'commoncomponents/LogoEditor';
import { useEffect, useRef, useState } from "react";
import { useSecurityStore } from "redi-security-components";
import { HttpPromise, HttpResult } from "redi-http";
import AttributeDropdownService from "../../services/attributeDropdownValue";
import AddressTypeService from "../../services/addressType";
import PartyService from "../../services/party";
import * as yup from "yup";
import { PartyTypeEnum } from "../../enum/PartyTypeEnum";
import IndustryAutocompleteMultiselectField from "../../components/IndustryAutocompleteMultiselectField/IndustryAutocompleteMultiselectField";
import withAsyncLoad from "../../components/HOC/withAsyncLoad";
import "./styles.scss";
import StickySubmit from "../../components/StickySubmit/StickySubmit";
import PageBody from "../../components/PageComponent/Body/PageBody";
import PageHeader from "../../components/PageComponent/Header/PageHeader";
import PageCard from "../../components/PageCard/PageCard";
import { useNavigate } from "react-router-dom";

const CountryAutocompleteField = withAsyncLoad<any>(() => import("commoncomponents/CountryAutocompleteField"));
const TimezoneAutocompleteField = withAsyncLoad<any>(() => import("commoncomponents/TimezoneAutocompleteField"));

type DropdownValues = {
    employeeOptions: GetListAttributeDropdownValueCDto[];
    addressTypeOptions: AddressTypeCDto[];
};

const defaultDropdownOptions:DropdownValues = {
    employeeOptions: [],
    addressTypeOptions: [],
};

const schema = yup.object<ExtendedPartyCDto>({
    organisation: yup.object({
        name: yup.string().required('Name is required')
    }),
    contactFields: yup.object({
        email_1: yup.string().email('Invalid email').required('Enter valid email')
    }),
	displayName: yup.string().required('Company Name is required'),
    fields: yup.object({
        employeeCount: yup.string().required('Employee Count is required'),
        industry: yup.string().required('Industry is required'),
        financialYearStart: yup.date().required('Financial Year Start is required'),
        timeZone: yup.string().required('Time Zone is required'),
        dateFormat: yup.string().required('Date Format is required'),
        firstDayOfWeek: yup.string().required('First Day of Week is required'),
    }),
    person: yup.object({}).nullable().transform((_, value) => {
        return Object.keys(value).length === 0 ? null : value;
    })
    // address_1: yup.object({
    //     lines: yup.string().required('Lines is required'),
    //     postalCode: yup.string().required('Postal Code is required'),
    //     countryCode: yup.string().required('Country Code is required'),
    // })
});

function Company() {
    const [ initialValues, setInitialValues ] = useState<ExtendedPartyCDto | null>(null);
    const [ dropdownValues, setDropdownValues ] = useState<DropdownValues>(defaultDropdownOptions);
    const [ isBusy, setIsBusy ] = useState(false);
    const [ isSaving, setIsSaving ] = useState(false);
    const { user } = useSecurityStore((state) => ({ user: state.user }));
    const navigate = useNavigate();
    const partyId = user?.partyId;
    const promiseRef1 = useRef<HttpPromise<ExtendedPartyCDto>>();
    const promiseRef2 = useRef<HttpPromise<GetListAttributeDropdownValueCDto[]>>();
    const promiseRef4 = useRef<HttpPromise<Array<AddressTypeCDto>>>();
    const promiseRef5 = useRef<HttpPromise<ExtendedPartyCDto>>();
    const promiseRef6 = useRef<HttpPromise<void>>();

    const handleIndustryOnChange = (form: FormikProps<ExtendedPartyCDto>) => (list: string) => {
        form.setFieldValue("fields.industry", list);
    };

    const handleCountryOnChange = (form: FormikProps<ExtendedPartyCDto>) => (country: CountryCDto) => {
        form.setFieldValue('address_1.countryCode', country?.countryCode ?? "");
        form.setFieldValue('address_1.countryName', country?.name ?? "");
    };

    const handleTimeZoneOnChange = (form: FormikProps<ExtendedPartyCDto>) => (timezone: TimezoneCDto) => {
        form.setFieldValue('fields.timeZone', timezone?.timeZoneId ?? "");
    };

    const save = async (data: ExtendedPartyCDto, actions: FormikHelpers<ExtendedPartyCDto>) => {
        try {
            if (isSaving) { return; }
            setIsSaving(true);
            //Update display name
            data.displayName = data.organisation.name;
            const casted = schema.cast(data) as ExtendedPartyCDto;
            promiseRef5.current = PartyService.update({data: casted, clearFields: {}});
            const response = await promiseRef5.current;
            if(partyId && data.avatarImageId) {
              promiseRef6.current = PartyService.UpdateAvatar(partyId, data.avatarImageId);
              const avatarResponse = await promiseRef6.current;
            }
            if(response.data && !response.error){
              navigate('/configuration');
            }
        } catch (error) {
        } finally {
            setIsSaving(false);
            actions.setSubmitting(false);
        }
    };

    const fetch = async () => {
        if (isBusy) { return; }
        setIsBusy(true);
        try {
            promiseRef1.current = PartyService.getRootForTenant(true, "CompanyProfile");
            promiseRef2.current = AttributeDropdownService.getListForAttribute("EmployeeCount");
            promiseRef4.current = AddressTypeService.getList();

            const results = await Promise.allSettled([promiseRef1.current, promiseRef2.current, promiseRef4.current]);
            const promise1Response: PromiseSettledResult<HttpResult<ExtendedPartyCDto>> = results[0];
            const promise2Response: PromiseSettledResult<HttpResult<GetListAttributeDropdownValueCDto[]>> = results[1];
            const promise4Response: PromiseSettledResult<HttpResult<Array<AddressTypeCDto>>> = results[2];

            if (promise1Response.status === "fulfilled") {
                const data = promise1Response.value;
                if (!data.error && data.data) {
                    setInitialValues(PartyService.getDefaultValues(PartyTypeEnum.Organisation, data.data));
                }
            }
            const dropdownValues:DropdownValues = {...defaultDropdownOptions};
            if (promise2Response.status === "fulfilled") {
                const data = promise2Response.value;
                if (!data.error && data.data) {
                    dropdownValues.employeeOptions = data.data;
                }
            }
            if (promise4Response.status === "fulfilled") {
                const data = promise4Response.value;
                if (!data.error && data.data) {
                    dropdownValues.addressTypeOptions = data.data;
                }
            }
            setDropdownValues(dropdownValues);
        } catch (error) {}
        finally {
            setIsBusy(false)
        }

    };

    const clearPromises = () => {
        if (promiseRef1.current?.cancel) {
            promiseRef1.current.cancel();
        }
        if (promiseRef2.current?.cancel) {
            promiseRef2.current.cancel();
        }
        if (promiseRef2.current?.cancel) {
            promiseRef2.current.cancel();
        }
        if (promiseRef4.current?.cancel) {
            promiseRef4.current.cancel();
        }
        if (promiseRef5.current?.cancel) {
            promiseRef5.current.cancel();
        }
        if (promiseRef6.current?.cancel) {
            promiseRef6.current.cancel();
        }
    };

    useEffect(() => {
        fetch();
        return clearPromises;
    }, []);
    
    const todaysDate = new Date();
    const day = todaysDate.getDate();
    const month = todaysDate.toLocaleString('default', { month: 'long' });
    const year = todaysDate.getFullYear();
    const dateFormatOptions =  [
        {
            code: "DD/MM/YYYY",
            label: `${day} ${month}, ${year}`
        },
        {
            code: "MM/DD/YYYY",
            label: `${month} ${day}, ${year}`
        },
        {
            code: "YYYY/MM/DD",
            label: `${year}, ${month} ${day}`
        }
    ];

    return (
        <div styleName="fixed-container-offset">
        <PageCard>
        <PageHeader
            title={"Company"}
            icon={["fad", "apartment"]}
            routeBackPath="configuration"
            routeBackText="back to configuration"
        />
        <PageBody>
        {isBusy ?
        <Box
            display="flex"
            height="100%"
            flexDirection="column"
            justifyContent="center"
            alignItems="center"
            textAlign="center"
        >
            <CircularProgress size={50} />...loading
        </Box>
        : initialValues ?
        <>
        <Formik<ExtendedPartyCDto>
            initialValues={initialValues}
            onSubmit={(data, actions) => {
                actions.setSubmitting(true);
                save(data, actions);
            }}
            enableReinitialize
            validationSchema={schema}
        >
            {(form) => (
                <form onSubmit={form.handleSubmit}>
                    <Grid container spacing={10}>
                        <Grid item sm={4} display={{ xs: "none", sm: "block"}}>
                            <HeadingInformation title="Details" bodyText="Details of your main business operations." />
                        </Grid>
                        <Grid item xs={12} sm={8}>
                            <div styleName="form-grid">
                                <FormHeader hideDivider={true}>Company Name</FormHeader>
                                <Field
                                    label={undefined}
                                    id="company-name"
                                    name="organisation.name"
                                    placeholder="Company Name"
                                    as={TextField}
                                    error={form.touched.organisation && getIn(form.touched.organisation, `name`) && Boolean(getIn(form.errors.organisation, `name`))}
                                    helperText={form.touched.organisation && getIn(form.touched.organisation, `name`) && getIn(form.errors.organisation, `name`)}
                                />
                                <FormHeader hideDivider={true}>Primary Business address</FormHeader>
                                <Field
                                    label={undefined}
                                    id="company-address_1_lines"
                                    name="address_1.lines"
                                    placeholder="Address"
                                    as={TextField}
                                    error={form.touched.address_1 && form.touched.address_1.lines && Boolean(form.errors.address_1?.lines )}
                                    helperText={form.touched.address_1 && form.touched.address_1.lines && form.errors.address_1?.lines}
                                />
                                <Field
                                    label={undefined}
                                    id="company-address_1.postalCode"
                                    name="address_1.postalCode"
                                    placeholder="Postal Code"
                                    as={TextField}
                                    error={form.touched.address_1 && form.touched.address_1.postalCode && Boolean(form.errors.address_1?.postalCode )}
                                    helperText={form.touched.address_1 && form.touched.address_1.postalCode && form.errors.address_1?.postalCode}
                                />
                                <Field id="company-address_1.country" name="address_1.countryCode">
                                    {(fieldProps: FieldProps<GetListAttributeDropdownValueCDto>) => (
                                        <CountryAutocompleteField
                                            id="company-address_1.countryCode"
                                            autoSelectFirst
                                            placeholder={"Country"}
                                            fieldDisplayText="name"
                                            fieldValue="countryCode"
                                            name={fieldProps.field.name}
                                            label={undefined}
                                            value={form.values.address_1.countryCode ?? ""}
                                            onChange={handleCountryOnChange(form)}
                                        />
                                    )}
                                </Field>
                                <Field
                                    label={undefined}
                                    id="company-contactFields_email_1"
                                    name="contactFields.email_1"
                                    placeholder="Billing Email"
                                    as={TextField}
                                    error={form.touched.contactFields && form.touched.contactFields.email_1 && Boolean(form.errors.contactFields?.email_1 )}
                                    helperText={form.touched.contactFields && form.touched.contactFields.email_1 && form.errors.contactFields?.email_1}
                                />
                            </div>
                        </Grid>
                        {/** Composition */}
                        <Grid item sm={4} display={{ xs: "none", sm: "block"}}>
                            <HeadingInformation title="Composition" bodyText="In this section we ask some approximate questions about your business and how it operates. As the system advances and we plan to introduce more sophisticated analytics into the platform to help you understand how you are performing against industry averages." />
                        </Grid>
                        <Grid item xs={12} sm={8}>
                            <FormHeader hideDivider={true}>Employees</FormHeader>
                            <Field
                                select
                                fullWidth
                                label={undefined}
                                id="company-fields.employeeCount"
                                name="fields.employeeCount"
                                placeholder="Employee Count"
                                as={TextField}
                                error={form.touched.fields && form.touched.fields.employeeCount&& Boolean(form.errors.fields?.employeeCount )}
                                helperText={form.touched.fields && form.touched.fields.employeeCount && form.errors.fields?.employeeCount}
                            >
                                {dropdownValues.employeeOptions.map((dropdownItem) => (
                                    <MenuItem key={dropdownItem.displayValue} value={dropdownItem.displayValue}>
                                        {dropdownItem.displayValue}
                                    </MenuItem>
                                ))}
                            </Field>
                            <FormHeader hideDivider={true}>Industry</FormHeader>
                            <Field id="company-fields.industry" name="fields.industry">
                                {(fieldProps: FieldProps<GetListAttributeDropdownValueCDto>) => (
                                    <IndustryAutocompleteMultiselectField
                                        id="company-fields.industry"
                                        fieldDisplayText="displayValue"
                                        fieldValue="value"
                                        name={fieldProps.field.name}
                                        label={undefined}
                                        value={form.values.fields.industry}
                                        onChange={handleIndustryOnChange(form)}
                                    />
                                )}
                            </Field>
                        </Grid>
                        {/** Business Setup */}
                        <Grid item sm={4} display={{ xs: "none", sm: "block"}}>
                            <HeadingInformation title="Business Setup" bodyText="Let us know the setup of your business and your preferences so we can customise the platform to you." />
                        </Grid>
                        <Grid item xs={12} sm={8}>
                            <FormHeader hideDivider={true}>Timezone</FormHeader>
                            <Field id="company-fields.timeZone" name="fields.timeZone">
                                {(fieldProps: FieldProps<GetListAttributeDropdownValueCDto>) => (
                                    <TimezoneAutocompleteField
                                        id="company-fields.timeZone"
                                        autoSelectFirst
                                        placeholder={"Timezone"}
                                        fieldDisplayText="name"
                                        fieldValue="timeZoneId"
                                        name={fieldProps.field.name}
                                        label={undefined}
                                        value={form.values.fields.timeZone ?? ""}
                                        onChange={handleTimeZoneOnChange(form)}
                                    />
                                )}
                            </Field>
                            <FormHeader hideDivider={true}>Financial Year Start Day</FormHeader>
                            <Field id="company-fields.financialYearStart" name="fields.financialYearStart">
                                {(fieldMeta: FieldProps) => (
                                <DatePicker
                                    label={undefined}
                                    views={['month', 'day']}
                                    inputFormat={form.values.fields.dateFormat.startsWith('DD') ? 'DD MMMM' : 'MMMM DD'}
                                    value={form.values.fields.financialYearStart ? dayjs(form.values.fields?.financialYearStart) : null}
                                    disableHighlightToday
                                    openTo='month'                                    
                                    minDate={`${new Date().getFullYear()}-1-1`}
                                    maxDate={`${new Date().getFullYear()}-12-31T23:59:59`}
                                    onChange={(val) => {
                                        form.setFieldValue(`fields.financialYearStart`, val)
                                    }}
                                    renderInput={(params) => 
                                        <TextField 
                                        {...params} 
                                        fullWidth={true}
                                        name={fieldMeta.field.name}
                                        label={undefined}
                                        placeholder={"Financial Year Start Day"}
                                        error={form.touched.fields && form.touched.fields.financialYearStart && Boolean(form.errors.fields?.financialYearStart )}
                                        helperText={form.touched.fields && form.touched.fields.financialYearStart && form.errors.fields?.financialYearStart}
                                        onBlur={fieldMeta.field.onBlur}
                                        />}
                                    />
                                )}
                            </Field>
                            {/* <FormHeader hideDivider={true}>Main Time Zone</FormHeader> */}

                                                    <FormHeader hideDivider={true}>Date Format</FormHeader>
                                                    <Field
                                                        id="company-fields.dateFormat"
                                                        name="fields.dateFormat"
                                                    >
                                                        {(fieldProps: FieldProps) => (
                                                            <RadioGroup
                                                                row={false}
                                                                {...fieldProps.field}
                                                            >
                                                                {dateFormatOptions.map((item, i) => {
                                                                    const { code, label } = item;
                                                                    return (
                                                                        <FormControlLabel key={`${i}_${code}`} value={code}  control={<Radio />} label={label} />
                                                                    );
                                                                })}
                                                            </RadioGroup>
                                                        )}
                                                    </Field>
                                                    <FormHeader hideDivider={true}>First Day of Week</FormHeader>
                                                    <Field
                                                        id="company-fields.firstDayOfWeek"
                                                        name="fields.firstDayOfWeek"
                                                    >
                                                        {(fieldProps: FieldProps) => (
                                                            <RadioGroup
                                                                row={false}
                                                                {...fieldProps.field}
                                                            >
                                                                <FormControlLabel key={`${1}_${"Monday"}`} value={"Monday"}  control={<Radio />} label={"Monday"} />
                                                                <FormControlLabel key={`${2}_${"Sunday"}`} value={"Sunday"}  control={<Radio />} label={"Sunday"} />
                                                            </RadioGroup>
                                                        )}
                                                    </Field>
                                                </Grid>
                                                {/** Business Branding */}
                                                <Grid item sm={4} display={{ xs: "none", sm: "block"}}>
                                                    <HeadingInformation title="Business Branding" bodyText="Upload your business logo so we can personalise your user experience. Please use a transparent background on your logo and ideally in SVG format for the best results." />
                                                </Grid>
                                                <Grid item xs={12} sm={8}>

                                                    <FormHeader hideDivider={true}>Logo</FormHeader>
                                                    {Boolean(partyId) &&
                                                        <LogoEditor
                                                            parentEntityId={partyId}
                                                            parentEntityType="Organisation"
                                                            currentImage={initialValues.avatarImageUrl}
                                                            updateImage={(newImage: FileCDto) => {
                                                              form.setFieldValue('avatarImageUrl', newImage.pathOrUrl);
                                                              form.setFieldValue('avatarImageId', newImage.fileId);
                                                            }} />}
                                                </Grid>
                                            </Grid>
                                            <StickySubmit isLoading={isSaving} handleSubmit={form.handleSubmit} buttonText="Save" />
                                        </form>
                                    )}
                                </Formik>
                            </> : null}
                </PageBody>
        </PageCard>
        </div>
    );
}

export default Company;

interface Props {}