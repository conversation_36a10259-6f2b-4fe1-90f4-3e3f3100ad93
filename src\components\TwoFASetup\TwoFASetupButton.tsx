import { Button, Container } from "@mui/material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useState } from "react";
import withAsyncLoad from "../HOC/withAsyncLoad";
import DrawerLayout from "../Drawer/Layout/DrawerLayout";

const TwoFASetupForm = withAsyncLoad<{ onClose?: () => void, onSave?: () => void }>(() => import("logincomponents/TwoFASetupForm"));

function TwoFASetupButton() {

  const [open, setOpen] = useState(false);

  const handleOnClickOpen = () => {
    setOpen(true);
  };

  const handleOnClickClose = () => {
    setOpen(false);
  }

  return (
    <>
      <Button onClick={handleOnClickOpen} color="primary" variant="contained">
        Setup 2FA<FontAwesomeIcon icon="shield-halved" />
      </Button>
      <DrawerLayout
        open={open}
        onClose={handleOnClickClose}
        title="Two Factor Authentication"
      >
        <Container className="drawer-body">
          {open ? <TwoFASetupForm onClose={handleOnClickClose} onSave={handleOnClickClose} /> : null}
        </Container>
      </DrawerLayout>
    </>
  );
}

export default TwoFASetupButton;