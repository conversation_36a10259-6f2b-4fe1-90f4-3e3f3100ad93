import { Box, Button, CircularProgress, FormControlLabel, FormGroup, FormHelperText, Grid, MenuItem, Select, SelectChangeEvent, Switch, TextField } from "@mui/material";
import { Field, FieldProps, Form, Formik, FormikHelpers, FormikProps, getIn } from "formik";
import { CountryCDto, ExtendedPartyCDto, FileCDto, GetListAttributeDropdownValueCDto } from "redi-types";
import FormHeader from "../../components/Form/Header/FormHeader";
import HeadingInformation from "../../components/FormStyles/HeadingInformation/HeadingInformation";
import LogoEditor from 'commoncomponents/LogoEditor';
import { useEffect, useRef, useState } from "react";
import { useSecurityStore } from "redi-security-components";
import { HttpPromise, HttpResult } from "redi-http";
import AttributeDropdownService from "../../services/attributeDropdownValue";
import PartyService from "../../services/party";
import * as yup from "yup";
import { PartyTypeEnum } from "../../enum/PartyTypeEnum";
import withAsyncLoad from "../../components/HOC/withAsyncLoad";
import StickySubmit from "../../components/StickySubmit/StickySubmit";
import PageBody from "../../components/PageComponent/Body/PageBody";
import PageHeader from "../../components/PageComponent/Header/PageHeader";
import "./styles.scss";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useNavigate } from "react-router-dom";
import { PhoneValidator } from "../../utils/phoneValidator";
import PageCard from "../../components/PageCard/PageCard";
import TwoFASetupButton from "../../components/TwoFASetup/TwoFASetupButton";
import { CountryCode, getCountryCallingCode } from "libphonenumber-js";
import MyPartyService from "../../services/myParty";

const CountryAutocompleteField = withAsyncLoad<any>(() => import("commoncomponents/CountryAutocompleteField"));

type DropdownValues = {
    employeeTitleOptions: GetListAttributeDropdownValueCDto[];
    employeeDepartmentOptions: GetListAttributeDropdownValueCDto[];
};

const defaultDropdownOptions:DropdownValues = {
    employeeTitleOptions: [],
    employeeDepartmentOptions: [],
};

const schema = yup.object<ExtendedPartyCDto>({
    person: yup.object({
        firstName: yup.string().required('First Name is required'),
        familyName: yup.string().required('Family Name is required'),
    }),
    contactFields: yup.object({
        email_1: yup.string().email('Invalid email').required('Enter valid email'),
        phone_1: yup.string().test('phoneTest', function (value: any) {
            const countryCode = this.parent.countryCode;
            const error = PhoneValidator.validate(value, countryCode);
            return error ? this.createError({ message: error }) : true;
        })
    }),
    fields: yup.object({
        employeeDepartment: yup.string().required('Employee Department is required'),
        employeeTitle: yup.string().required('Employee Title is required')
    }),
    notificationConfig: yup.object({
      Global: yup.object({
        WhatsApp: yup.object({
          isEnabled: yup.bool().required()
        })
      })
    }).test('whatsAppNotifs', 'A valid phone number is required to enable WhatsApp Notifications',
    (config, context) => {
      const countryCode = context.parent.countryCode;
      const phone = context.parent.contactFields.phone_1;
      const enabled = config.Global.WhatsApp.isEnabled;
      const valid = !enabled || (phone && !PhoneValidator.validate(phone, countryCode))
      return valid;
    }),
    organisation: yup.object({}).nullable().transform((_, value) => {
        return Object.keys(value).length === 0 ? null : value;
    })
    // address_1: yup.object({
    //     lines: yup.string().required('Lines is required'),
    //     postalCode: yup.string().required('Postal Code is required'),
    //     countryCode: yup.string().required('Country Code is required'),
    // })
});

/**
 * Extract the Country Code from the Phone Number value
 * Extract the Phone Number value without the Caller Code
 * @param values 
 * @returns Return the Phone number without the Country Code, and Country code
 */
function getPhoneNumberDetails(values: ExtendedPartyCDto) {
    let countryCode = values.countryCode;
    let phoneNumber = values.contactFields?.phone_1;
    const sections = phoneNumber ? phoneNumber.split(" ") :  undefined;
    if (phoneNumber && sections && sections.length > 1) {
        const callerCode = phoneNumber.split(" ")[0];    
        phoneNumber = sections.slice(1).join(" ");     
        if (PhoneValidator.callerCodeToCountryCode.hasOwnProperty(callerCode)) {
            countryCode = PhoneValidator.callerCodeToCountryCode[callerCode];
        }
    }
    return { phoneNumber, countryCode }
}

function Profile() {
    const [ initialValues, setInitialValues ] = useState<ExtendedPartyCDto | null>(null);
    const [ dropdownValues, setDropdownValues ] = useState<DropdownValues>(defaultDropdownOptions);
    const [ isBusy, setIsBusy ] = useState(false);
    const [ isSaving, setIsSaving ] = useState(false);
    const { user, setUser } = useSecurityStore((state) => ({ user: state.user, setUser: state.setUser }));

    const navigate = useNavigate();
    const partyId = user?.partyId;
    const promiseRef1 = useRef<HttpPromise<ExtendedPartyCDto>>();
    const promiseRef2 = useRef<HttpPromise<GetListAttributeDropdownValueCDto[]>>();
    const promiseRef3 = useRef<HttpPromise<GetListAttributeDropdownValueCDto[]>>();
    const promiseRef4 = useRef<HttpPromise<ExtendedPartyCDto>>();
    const promiseRef5 = useRef<HttpPromise<void>>();

    const handleCountryOnChange = (form: FormikProps<ExtendedPartyCDto>) => (country: CountryCDto) => {
        form.setFieldValue('address_1.countryCode', country?.countryCode ?? "");
        form.setFieldValue('address_1.countryName', country?.name ?? "");
    };

    const handleRouteChangePassword = () => {
        navigate("/reset-password");
    };

    const updateStoredUser = (data:ExtendedPartyCDto) => {
        if (user) {
            setUser({
                ...user,
                firstName: data.person.firstName!,
                lastName: data.person.familyName!,
                fullName: data.displayName!,
                partyAvatarImageUrl: data.avatarImageUrl,
                email: data.contactFields.email_1
            });
        }
    };

    const save = async (data: ExtendedPartyCDto, actions: FormikHelpers<ExtendedPartyCDto>) => {
      try {
          if (isSaving) { return; }
          actions.setSubmitting(true);
          setIsSaving(true);
          const values:ExtendedPartyCDto = {
            ...data,
            //Update display name
            displayName: data.person.firstName + " " + data.person.familyName,
            //Extracted caller code added back into start of number
            contactFields: data.contactFields?.phone_1 ? {
                ...data.contactFields,
                phone_1: getCountryCallingCode(data.countryCode as CountryCode) + " " + data.contactFields.phone_1
            } : data.contactFields
          };
          const casted = schema.cast(values) as ExtendedPartyCDto;
          promiseRef4.current = MyPartyService.update({data: casted, clearFields: {}});
          const response = await promiseRef4.current;
          if(partyId && data.avatarImageId) {
            promiseRef5.current = MyPartyService.updateAvatar(data.avatarImageId);
            const avatarResponse = await promiseRef5.current;
          }
          if(response.data && !response.error){
            updateStoredUser(values);
            navigate('/configuration');
          }
      } catch (error) {
      } finally {
          setIsSaving(false);
          actions.setSubmitting(false);
      }
    };

    const fetch = async () => {
        if (isBusy || !partyId) { return; }
        setIsBusy(true);
        try {
            promiseRef1.current = MyPartyService.get(true, "EmployeeProfile", undefined, undefined, undefined, true);
            promiseRef2.current = AttributeDropdownService.getListForAttribute("EmployeeTitle");
            promiseRef3.current = AttributeDropdownService.getListForAttribute("EmployeeDepartment");

            const results = await Promise.allSettled([promiseRef1.current, promiseRef2.current, promiseRef3.current]);
            const promise1Response: PromiseSettledResult<HttpResult<ExtendedPartyCDto>> = results[0];
            const promise2Response: PromiseSettledResult<HttpResult<GetListAttributeDropdownValueCDto[]>> = results[1];
            const promise3Response: PromiseSettledResult<HttpResult<GetListAttributeDropdownValueCDto[]>> = results[2];

            if (promise1Response.status === "fulfilled") {
                const data = promise1Response.value;
                if (!data.error && data.data) {
                    const values = PartyService.getDefaultValues(PartyTypeEnum.Person, data.data);
                    if (values.contactFields?.phone_1) {
                        const { phoneNumber, countryCode } = getPhoneNumberDetails(values);
                        values.contactFields.phone_1 = phoneNumber; //Modified to remove caller code
                        values.countryCode = countryCode; //Extracted country code to be used in select dropdown
                    }
                    setInitialValues(values);
                }
            }
            const dropdownValues:DropdownValues = {...defaultDropdownOptions};
            if (promise2Response.status === "fulfilled") {
                const data = promise2Response.value;
                if (!data.error && data.data) {
                    dropdownValues.employeeTitleOptions = data.data;
                }
            }
            if (promise3Response.status === "fulfilled") {
                const data = promise3Response.value;
                if (!data.error && data.data) {
                    dropdownValues.employeeDepartmentOptions = data.data;
                }
            }
            setDropdownValues(dropdownValues);
        } catch (error) {}
        finally {
            setIsBusy(false)
        }

    };

    const clearPromises = () => {
        if (promiseRef1.current?.cancel) {
            promiseRef1.current.cancel();
        }
        if (promiseRef2.current?.cancel) {
            promiseRef2.current.cancel();
        }
        if (promiseRef2.current?.cancel) {
            promiseRef2.current.cancel();
        }
        if (promiseRef4.current?.cancel) {
            promiseRef4.current.cancel();
        }
        if (promiseRef5.current?.cancel) {
            promiseRef5.current.cancel();
        }
    };

    useEffect(() => {
        fetch();
        return clearPromises;
    }, []);

    return (
        <div styleName="fixed-container-offset">
        <PageCard>
        <PageHeader 
            title={"Your Profile"} 
            icon={["fad", "id-card-clip"]} 
            routeBackPath="configuration"
            routeBackText="back to configuration"
        />
        <PageBody>
        {isBusy ?
        <Box
            display="flex"
            height="100%"
            flexDirection="column"
            justifyContent="center"
            alignItems="center"
            textAlign="center"
        >
            <CircularProgress size={50} />...loading
        </Box>
        : initialValues ?
        <>
        <Formik<ExtendedPartyCDto>
            initialValues={initialValues}
            onSubmit={save}
            enableReinitialize
            validationSchema={schema}
        >
            {(form) => {
                const validPhone = !!form.values.contactFields?.phone_1 && !form.errors.contactFields?.phone_1;

              return (
                <Form>
                    <Grid container spacing={10}>
                        <Grid item sm={4} display={{ xs: "none", sm: "block"}}>
                            <HeadingInformation title="Details" bodyText="Details about yourself." />
                        </Grid>
                        <Grid item xs={12} sm={8}>
                            <div styleName="form-grid">
                                <FormHeader hideDivider={true}>Your Name</FormHeader>
                                <Field
                                    label={undefined}
                                    id="person-firstName"
                                    name="person.firstName"
                                    placeholder="First Name"
                                    as={TextField}
                                    error={form.touched.person && getIn(form.touched.person, `firstName`) && Boolean(getIn(form.errors.person, `firstName`))}
                                    helperText={form.touched.person && getIn(form.touched.person, `firstName`) && getIn(form.errors.person, `firstName`)}
                                />
                                <Field
                                    label={undefined}
                                    id="person-familyName"
                                    name="person.familyName"
                                    placeholder="Family Name"
                                    as={TextField}
                                    error={form.touched.person && getIn(form.touched.person, `familyName`) && Boolean(getIn(form.errors.person, `familyName`))}
                                    helperText={form.touched.person && getIn(form.touched.person, `familyName`) && getIn(form.errors.person, `familyName`)}
                                />
                                <FormHeader hideDivider={true}>Email</FormHeader>
                                <Field
                                    label={undefined}
                                    id="profile-contactFields_email_1"
                                    name="contactFields.email_1"
                                    placeholder="Email"
                                    as={TextField}
                                    error={form.touched.contactFields && form.touched.contactFields.email_1 && Boolean(form.errors.contactFields?.email_1 )}
                                    helperText={form.touched.contactFields && form.touched.contactFields.email_1 && form.errors.contactFields?.email_1}
                                />
                                <FormHeader hideDivider={true}>Phone / WhatsApp Number</FormHeader>
                                <div styleName="flex">
                                    <div styleName="caller-code-input">
                                    <Field
                                        fullWidth
                                        select
                                        id="profile-countryCode"
                                        name="countryCode"
                                        as={TextField}
                                        variant="outlined"
                                    >
                                        {PhoneValidator.countryCodes.map((item, i) => (
                                            <MenuItem key={item} value={item}>{item + " " + getCountryCallingCode(item as CountryCode)}</MenuItem>
                                        ))}
                                    </Field>
                                    </div>
                                    <div styleName="phone-input">
                                    <Field
                                        fullWidth
                                        label={undefined}
                                        id="profile-contactFields_phone_1"
                                        name="contactFields.phone_1"
                                        placeholder="Phone / WhatsApp Number"
                                        as={TextField}
                                        error={form.touched.contactFields && form.touched.contactFields.phone_1 && Boolean(form.errors.contactFields?.phone_1 )}
                                        helperText={form.touched.contactFields && form.touched.contactFields.phone_1 && form.errors.contactFields?.phone_1}
                                    />
                                    </div>
                                </div>
                                <FormHeader hideDivider={true}>Location</FormHeader>
                                <Field
                                    label={undefined}
                                    id="profile-address_1_lines"
                                    name="address_1.lines"
                                    placeholder="Address"
                                    as={TextField}
                                    error={form.touched.address_1 && form.touched.address_1.lines && Boolean(form.errors.address_1?.lines )}
                                    helperText={form.touched.address_1 && form.touched.address_1.lines && form.errors.address_1?.lines}
                                />
                                <Field
                                    label={undefined}
                                    id="profile-address_1.postalCode"
                                    name="address_1.postalCode"
                                    placeholder="Postal Code"
                                    as={TextField}
                                    error={form.touched.address_1 && form.touched.address_1.postalCode && Boolean(form.errors.address_1?.postalCode )}
                                    helperText={form.touched.address_1 && form.touched.address_1.postalCode && form.errors.address_1?.postalCode}
                                />
                                <Field id="profile-address_1.country" name="address_1.countryCode">
                                    {(fieldProps: FieldProps<GetListAttributeDropdownValueCDto>) => (
                                        <CountryAutocompleteField
                                            id="profile-address_1.countryCode"
                                            autoSelectFirst
                                            placeholder={"Country"}
                                            fieldDisplayText="name"
                                            fieldValue="countryCode"
                                            name={fieldProps.field.name}
                                            label={undefined}
                                            value={form.values.address_1.countryCode ?? ""}
                                            onChange={handleCountryOnChange(form)}
                                        />
                                    )}
                                </Field>
                            </div>
                        </Grid>
                        {/** Photo / Avatar */}
                        <Grid item sm={4} display={{ xs: "none", sm: "block"}}>
                            <HeadingInformation title="Photo / Avatar" bodyText="Your unique identity" />
                        </Grid>
                        <Grid item xs={12} sm={8}>
                            <FormHeader hideDivider={true}>Image</FormHeader>
                            {Boolean(partyId) &&
                            <LogoEditor 
                                parentEntityId={partyId} 
                                parentEntityType="Person"
                                currentImage={initialValues.avatarImageUrl} 
                                updateImage={(newImage: FileCDto) => {
                                  form.setFieldValue('avatarImageUrl', newImage.pathOrUrl);
                                  form.setFieldValue('avatarImageId', newImage.fileId);
                                }}/>}
                        </Grid>
                        {/** Employment */}
                        <Grid item sm={4} display={{ xs: "none", sm: "block"}}>
                            <HeadingInformation title="Employment" bodyText="Details of your position in the company. This will allow us to tailor your experiences." />
                        </Grid>
                        <Grid item xs={12} sm={8}>
                            <FormHeader hideDivider={true}>Department</FormHeader>
                            <Field
                                select
                                fullWidth
                                label={undefined}
                                id="profile-fields.employeeDepartment"
                                name="fields.employeeDepartment"
                                placeholder="Department"
                                as={TextField}
                                error={form.touched.fields && form.touched.fields.employeeDepartment && Boolean(form.errors.fields?.employeeDepartment )}
                                helperText={form.touched.fields && form.touched.fields.employeeDepartment && form.errors.fields?.employeeDepartment}
                            >
                                {dropdownValues.employeeDepartmentOptions.map((dropdownItem) => (
                                    <MenuItem key={dropdownItem.displayValue} value={dropdownItem.displayValue}>
                                        {dropdownItem.displayValue}
                                    </MenuItem>
                                ))}
                            </Field>
                            <FormHeader hideDivider={true}>Role</FormHeader>
                            <Field
                                select
                                fullWidth
                                label={undefined}
                                id="profile-fields.employeeTitle"
                                name="fields.employeeTitle"
                                placeholder="Role"
                                as={TextField}
                                error={form.touched.fields && form.touched.fields.employeeTitle && Boolean(form.errors.fields?.employeeTitle )}
                                helperText={form.touched.fields && form.touched.fields.employeeTitle && form.errors.fields?.employeeTitle}
                            >
                                {dropdownValues.employeeTitleOptions.map((dropdownItem) => (
                                    <MenuItem key={dropdownItem.displayValue} value={dropdownItem.displayValue}>
                                        {dropdownItem.displayValue}
                                    </MenuItem>
                                ))}
                            </Field>
                        </Grid>

                        {/** Notifications */}
                        <Grid item sm={4} display={{ xs: "none", sm: "block"}}>
                            <HeadingInformation title="Notifications" bodyText="Enable notifications to receive alerts on key events within the system such as when an integration stops working. Note: WhatsApp requires you to enter your phone number above." />
                        </Grid>
                        <Grid item xs={12} sm={8} styleName='notification-toggle-container'>
                          <FormGroup>
                            <div styleName='notification-toggle one-line'>
                                    <FormControlLabel
                                        control={
                                            <Field
                                                label={undefined}
                                                id="profile-notificationConfig.Global.Email.isEnabled"
                                                name="notificationConfig.Global.Email.isEnabled"
                                                as={Switch}
                                                disabled={!form.values.contactFields?.email_1 || form.errors.contactFields?.email_1}
                                                checked={!!form.values.contactFields?.email_1 && !form.errors.contactFields?.email_1 && form.values.notificationConfig?.Global?.Email?.isEnabled} />}
                                        label={<FormHeader hideDivider={true}>Email</FormHeader>}
                                    />
                            </div>
                            <div styleName='notification-toggle one-line'>
                                    <FormControlLabel
                                        control={
                                            <Field
                                                label={undefined}
                                                id="profile-notificationConfig.Global.WhatsApp.isEnabled"
                                                name="notificationConfig.Global.WhatsApp.isEnabled"
                                                as={Switch}
                                                disabled={!validPhone && !form.values.notificationConfig?.Global?.WhatsApp?.isEnabled}
                                                value={validPhone && form.values.notificationConfig?.Global?.WhatsApp?.isEnabled}
                                                checked={form.values.notificationConfig?.Global?.WhatsApp?.isEnabled}/>}
                                        label={<FormHeader hideDivider={true}>WhatsApp</FormHeader>}
                                    />
                                    { !validPhone ? 
                                    <FormHelperText id='whatsapp-notif-help' style={{color: form.values.notificationConfig?.Global?.WhatsApp?.isEnabled ? 'red' : undefined}}>
                                      A valid phone number is required to enable WhatsApp Notifications
                                    </FormHelperText> 
                                    : null }
                            </div>
                          </FormGroup>
                        </Grid>

                        {/** Password */}
                        <Grid item sm={4} display={{ xs: "none", sm: "block"}}>
                            <HeadingInformation title="Password" bodyText="This is your unique password to access the system. We recommend that you change your password frequently and use a randomly generated password combined with a password manager for maximum security." />
                        </Grid>
                        <Grid item xs={12} sm={8} styleName='change-password-btn'>
                            <Button onClick={handleRouteChangePassword} color="primary" variant="contained">
                                Change Password <FontAwesomeIcon icon="lock-alt" />
                            </Button>
                        </Grid>

                        {/** Two Factor Authentication */}
                        <Grid item sm={4} display={{ xs: "none", sm: "block"}}>
                            <HeadingInformation title="Two Factor Authentication" bodyText="We recommend keeping your account secure by enabling 2FA by using a one-time passcode (TOTP) from an authenticator app." />
                        </Grid>
                        <Grid item xs={12} sm={8} marginTop='25px'>
                              <div>
                                  <FormGroup>
                                      <FormControlLabel
                                          control={
                                              <Field
                                                  label="Enabled"
                                                  id="profile-fields.twoFactorEnabled"
                                                  name="fields.twoFactorEnabled"
                                                  as={Switch}
                                                  value={form.values.fields.twoFactorEnabled}
                                                  onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                                                    form.setFieldValue("fields.twoFactorEnabled", event.target.checked ? "1" : "0" )
                                                  }}
                                                  checked={form.values.fields.twoFactorEnabled === "1"} />}
                                          label={<FormHeader hideDivider={true}>Enabled</FormHeader>}
                                      />
                                  </FormGroup>
                              </div>
                              <div>
                                  <TwoFASetupButton />
                              </div>
                        </Grid>
                    </Grid>
                    <StickySubmit isLoading={isSaving} handleSubmit={form.handleSubmit} buttonText="Save" />
                </Form>
              )}}
        </Formik>
        </> : null}
        </PageBody>
        </PageCard>
        </div>
    );
}

export default Profile;

interface Props {}