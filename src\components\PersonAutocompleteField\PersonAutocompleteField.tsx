import { StandardListParameters, ExtendedPartyCDto } from "redi-types";
import RediAutocompleteField, { AutocompleteProps } from "../RediField/Autocomplete/RediAutocompleteField";
import PartyService from "../../services/party";

function PersonAutocompleteField(props: Props) {

    const { value, params, ...other } = props;

    const standardListParameters = params?.standardListParameters ?? {
        limit: 50,
        offset: 0,
        sortBy: 'DisplayName',
        isDeleted: false,
        forceNullBottom: true
    };

    const handleCall = (query?: string) => {
        return PartyService.getListPersonsQuery(
            standardListParameters,
            query,
            params?.hasRoleCode,
            params?.statusCode,
            params?.showInactiveRole,
            params?.getPartyAttributes,
            params?.attributeGroupCodes,
            params?.attributeFields,
            params?.statisticFields,
            params?.parentRelationFields,
            params?.childRelationFields,
            params?.contactMethodFields,
            params?.addressFields,
            params?.filter
        );
    };
    
    return (
        <RediAutocompleteField 
            {...other}
            fieldDisplayText="displayName"
            fieldValue="partyId"
            value={value}
            callService={handleCall}
        />
    );
}

interface Props extends Omit<AutocompleteProps, "callService"> {
    value: ExtendedPartyCDto | null;
    params?: {
        standardListParameters: StandardListParameters,
        hasRoleCode?: string,
        statusCode?: string,
        showInactiveRole?: boolean,
        getPartyAttributes?: boolean,
        attributeGroupCodes?: string,
        attributeFields?: string,
        statisticFields?: string,
        parentRelationFields?: string,
        childRelationFields?: string,
        contactMethodFields?: string,
        addressFields?: string,
        filter?: { [key: string]: string }
    }
}

export default PersonAutocompleteField;