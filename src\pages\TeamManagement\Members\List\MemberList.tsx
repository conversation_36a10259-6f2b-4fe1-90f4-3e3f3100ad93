import { DataGrid, GridColDef, GridColumnMenuContainer, GridColumnMenuFilterItem } from '@mui/x-data-grid';
import { useEffect, useMemo, useRef, useState } from 'react';
import { HttpPromise, HttpResult } from 'redi-http';
import { ExtendedPartyCDto, GetListPartyRelationshipCDto, GetListRoleRelationshipCDto, ListResponseDto } from 'redi-types';
import { Avatar, Box, Button, Card, Grid, Menu, MenuItem, Typography } from '@mui/material';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useNavigate, useParams } from 'react-router-dom';
import "./styles.scss";
import React from 'react';
import PageHeader from '../../../../components/PageComponent/Header/PageHeader';
import PageBody from '../../../../components/PageComponent/Body/PageBody';
import PageCard from '../../../../components/PageCard/PageCard';
import { withAuthenticate } from 'redi-security-components';
import { useMediaQuery } from '@mui/material';
import { dialog } from 'redi-formik-material';
import PartyRelationshipService from '../../../../services/partyrelationship';
import MemberManager from '../Manager/MemberManager';
import PartyService from '../../../../services/party';
import FontAwesomeIconButton from '../../../../components/FontAwesomeIconButton/FontAwesomeIconButton';
import { ClaimTypeEnum } from '../../../../enum/ClaimTypeEnum';
import Unauthorised from '../../../Error/Unauthorised';

function ThreeDotMenu({
    party,
    onRefresh
}: {
    party: GetListPartyRelationshipCDto,
    onRefresh: () => void
}) {
    const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);
    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);

    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleDeleteClick = () => {
        dialog({
            title: "Confirmation",
            bodyText: "Are you sure you want to save?",
            agreeText: 'OK',
            disagreeText: 'Cancel',
            onClick: () => {
                PartyRelationshipService.Delete(party.partyRelationshipId!).then((data) => {
                    setAnchorEl(null);
                    if (!data.error) {
                        onRefresh();
                    }
                });
            },
            onClose: () => {
                setAnchorEl(null);
            }
        });
    }

    return (
        <div>
            <FontAwesomeIconButton onClick={handleClick} icon={["fad", "ellipsis-vertical"]} />
            <Menu
                id="long-menu"
                MenuListProps={{ 'aria-labelledby': 'long-button' }}
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                slotProps={{
                    paper: {
                        style: {
                            maxHeight: 300,
                            width: '20ch',
                        },
                    }
                }}
            >
                <MenuItem key={"delete-button"} onClick={handleDeleteClick}>
                    Remove Member
                </MenuItem>
            </Menu>
        </div>
    );
}

function getColumns(refresh: () => void) {
    const columns: GridColDef<GetListPartyRelationshipCDto>[] = [
        {
            field: 'toPartyAvatarImageUrl',
            headerName: '',
            sortable: false,
            width: 80,
            disableColumnMenu: true,
            align: "center",
            renderCell: (params: any) => {
                return (
                    <Box display="grid" height="100%" justifyContent="center" alignItems="center">
                        <Avatar styleName='image-circle'src={params.row.toPartyAvatarImageUrl} />
                    </Box>
                );
            }
        },
        {
            field: 'toPartyName',
            headerName: 'Fullname',
            flex: 1,
            sortable: true,
            hideable: false,

        },
        {
            field: 'action',
            headerName: 'Actions',
            sortable: false,
            flex: 0.5,
            disableColumnMenu: true,
            cellClassName: 'action-cell',
            renderCell: (params: any) => {
                return (
                    <ThreeDotMenu
                        party={params.row}
                        onRefresh={refresh}
                    />
                );
            }
        }
    ];

    return columns;
}

function getRowId(row: GetListPartyRelationshipCDto) {
    return row.partyRelationshipId!;
}

function MemberList() {
    const { id } = useParams();

    const fetchList = async () => {
        setIsBusy(true);
        promise1Ref.current = refresh();
        try {
            const response = await promise1Ref.current;
            if (!response.error && response.data && response.data.list) {
                setList(response.data.list);
            }
        } finally {
            setIsBusy(false);
            setIsInitialLoading(false);
        }
    }

    const [list, setList] = useState<GetListPartyRelationshipCDto[]>([]);
    const [party, setParty] = useState<ExtendedPartyCDto>();
    const [isBusy, setIsBusy] = useState(false);
    const [isInitialLoading, setIsInitialLoading] = useState(true);
    const [openEditTeam, setOpenEditTeam] = useState(false);
    const columns = useMemo(() => getColumns(fetchList), []);
    const navigate = useNavigate();
    const promise1Ref = useRef<HttpPromise<ListResponseDto<GetListPartyRelationshipCDto>>>();
    const promise2Ref = useRef<HttpPromise<ExtendedPartyCDto>>();

    useEffect(() => {
        const doCall = async () => {
            try {
                promise1Ref.current = refresh();
                promise2Ref.current = PartyService.get(id!, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, "Team");

                const results = await Promise.allSettled([promise1Ref.current, promise2Ref.current]);
                const promise1: PromiseSettledResult<HttpResult<ListResponseDto<GetListPartyRelationshipCDto>>> = results[0];
                const promise2: PromiseSettledResult<HttpResult<ExtendedPartyCDto>> = results[1];

                if (promise1.status === "fulfilled") {
                    const data = promise1.value;
                    if (!data.error && data.data && data.data.list) {
                        setList(data.data.list);
                    }
                }
                if (promise2.status === "fulfilled") {
                    const data = promise2.value;
                    if (!data.error && data.data) {
                        setParty(data.data);
                    }
                }
            } catch (error) { }
            finally {
                setIsInitialLoading(false);
            }
        };
        doCall();

        return clearPromises;
    }, []);

    const clearPromises = () => {
        if (promise1Ref.current?.cancel) {
            promise1Ref.current.cancel();
        }
        if (promise2Ref.current?.cancel) {
            promise2Ref.current.cancel();
        }
    }
    const refresh = () => {
        return PartyRelationshipService.getListForPartyId(id!, {
            limit: 999,
            offset: 0,
            sortBy: 'ToPartyName',
            isDeleted: false,
            forceNullBottom: true
        }, "Employee", "Membership", undefined, undefined, undefined, true);
    };

    const handleClose = () => {
        setOpenEditTeam(false);
    };

    const handleSave = () => {
        fetchList();
        handleClose();
    };

    const handleAddNew = () => {
        setOpenEditTeam(true);
    };

    const handleClickRow = (params: any) => {
        if (params.field !== "action") {
            navigate(`/settings/teams`);
        }
    };

    return (
        <PageCard>
            <div>
                <PageHeader
                    title={party?.displayName ?? ""}
                    secondaryText=""
                    icon={["fad", "screen-users"]}
                    showBackButton={true}
                    routeBackPath="settings/teams"
                    routeBackText="back to teams"
                />
                <PageBody isLoading={isInitialLoading}>
                    {list.length === 0 ?
                        <Box
                            display="flex"
                            height="100%"
                            flexDirection="column"
                            justifyContent="center"
                            alignItems="center"
                            textAlign="center"
                        >
                            <Typography color="text.secondary" variant="body1">
                                Thie team current does not have any members, add your first member by clicking 'Add Member'
                                <br /><br />
                                <Button variant="contained" onClick={handleAddNew}>Add Member <FontAwesomeIcon icon={"plus"} /></Button>
                            </Typography>
                        </Box> :
                        <Grid container>
                            <Grid item xs={12} textAlign="end">
                                <Card className="page-actions-bar">
                                    <Button variant="contained" onClick={handleAddNew}>Add Member <FontAwesomeIcon icon={"plus"} /></Button>
                                </Card>
                            </Grid>
                            <Grid item xs={12}>
                                <div style={{ height: "-webkit-fill-available", display: "grid", overflow: "auto", gridAutoRows: "100%" }}>
                                    <DataGrid
                                        className={"primary-data-grid-list data-grid-container"}
                                        getRowClassName={() => ("data-grid-row-hover")}
                                        getRowId={getRowId}
                                        hideFooter={list?.length <= 5}
                                        loading={isBusy}
                                        rows={list}
                                        columns={columns}
                                        initialState={{
                                            pagination: {
                                                paginationModel: {
                                                    pageSize: 5,
                                                },
                                            },
                                        }}
                                        pageSizeOptions={[5, 25, 100]}
                                        onCellClick={handleClickRow}
                                        disableRowSelectionOnClick
                                        disableColumnResize={true}
                                    />
                                </div>
                            </Grid>
                        </Grid>}
                        <MemberManager open={openEditTeam} name={party?.displayName!} partyRoleId={party?.partyRoles![0]?.partyRoleId!} partyRelationTypeCode="Membership" members={list} onSave={handleSave} onClose={handleClose} />
                </PageBody>
            </div>
        </PageCard>
    );
}

export default withAuthenticate(MemberList, { requiredClaims: [], unauthorisedRender: <Unauthorised /> });