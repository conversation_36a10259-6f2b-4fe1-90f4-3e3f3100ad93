import { MenuItem } from 'redi-types';
import withAsyncLoad from '../components/HOC/withAsyncLoad';
import { ClaimTypeEnum } from '../enum/claimTypeEnum';

const Dashboard = withAsyncLoad(() => import("../pages/Dashboard/Dashboard/Dashboard"));
const PresentationList = withAsyncLoad(() => import("../pages/Presentation/List/PresentationList"));
const Presentation = withAsyncLoad(() => import("../pages/Presentation/Presentation/Presentation"));
/* Routes in this file are exported to the host-container */

const routes: MenuItem[] = [

];

export default routes;