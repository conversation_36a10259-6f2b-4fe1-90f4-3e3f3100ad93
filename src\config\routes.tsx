import { MenuItem } from 'redi-types';
import withAsyncLoad from '../components/HOC/withAsyncLoad';
import { ClaimTypeEnum } from '../enum/ClaimTypeEnum';

const Company = withAsyncLoad(() => import('../pages/Company/Company'));
const Profile = withAsyncLoad(() => import('../pages/Profile/Profile'));
const TeamList = withAsyncLoad(() => import('../pages/TeamManagement/Teams/List/TeamList'));
const Members = withAsyncLoad(() => import('../pages/TeamManagement/Members/List/MemberList'));
const Membership = withAsyncLoad(() => import('../pages/TeamManagement/Membership/Membership'));

const routes: MenuItem[] = [

    {
        path: "settings/profile",
        name: "Edit Profile",
        element: <Profile />,
        //requiredClaims: [ClaimTypeEnum.EditProfile],
        excludeFromMenu: true
    },
    {
        path: "settings/company",
        name: "Edit Company",
        element: <Company />,
        requiredClaims: [ClaimTypeEnum.EditCompany],
        excludeFromMenu: true
    },
    {
        path: "settings/teams",
        name: "Teams",
        element: <TeamList />,
        //requiredClaims: [ClaimTypeEnum.EditCompany]
        excludeFromMenu: true
    },
    {
        path: "settings/team/:id",
        name: "Members",
        element: <Members />,
        //requiredClaims: [ClaimTypeEnum.EditCompany],
        excludeFromMenu: true
    },
    {
        path: "settings/my-teams",
        name: "My Team",
        element: <Membership />,
        //requiredClaims: [ClaimTypeEnum.EditCompany],
        excludeFromMenu: true
    }
];

export default routes;