
import { Box, Button, Container, useTheme } from '@mui/material';
import './styles.scss';


interface Props {
  isLoading: boolean;
  handleSubmit: () => void;
  buttonText: string;
}
function StickySubmit(props: Props) {
  const { isLoading, handleSubmit, buttonText } = props;
  const theme = useTheme();
  return (
    <Box bgcolor={theme.palette.background.paper} styleName="sticky-container">
      <Button
        type="submit"
        variant="contained"
        disabled={isLoading}
        onClick={handleSubmit}

      >{buttonText}</Button>
    </Box>
  );
}

export default StickySubmit;