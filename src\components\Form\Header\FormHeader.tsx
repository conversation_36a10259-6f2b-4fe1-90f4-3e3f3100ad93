import { Typography } from "@mui/material";

function FormHeader(props: Props) {

    const { children, hideDivider } = props;

    return (
        <div style={{marginBottom: "15px", marginTop: "15px"}}>
            <Typography variant="subtitle1" fontWeight="bold" component="div">{children}</Typography>
            {!hideDivider ?
            <div style={{display: "flex", marginTop: "5px"}}>
                <svg width="200" height="4" xmlns="http://www.w3.org/2000/svg">
                    <path d="M0 1 L130 1 Q130 1, 130 1 L220 1" stroke="#952EFD" strokeWidth="4" fill="transparent" strokeLinecap="round" />
                </svg>
            </div> : null}
        </div>
    );
}

interface Props {
    children: React.ReactNode;
    hideDivider?: boolean;
}

export default FormHeader;