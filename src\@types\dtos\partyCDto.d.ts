import { NotificationChannelEnum, NotificationTypeEnum } from "../../enum/NotificationConfigEnum";

declare module "redi-types" {
    export interface BasePartyCDto extends DtoBase {
        partyId?: string;
        displayName?: string;
        aliasName?: string;
        shortName?: string;
        statusCode: string;
        avatarImageId?: string;
        userId?: string;
        partyType?: string;
        avatarImageUrl?: string;
    }

    /**
     * Party Dto with Person and Organisation fields. The use of Record<string, never> is to force an empty object so it works well with Formik's validation syntax.
     */
    export interface PartyCDto extends BasePartyCDto {
        roleTypeCode?: string;
        /**
         * Person party member fields.
         */
        person: GetPersonCDto | Record<string, never>; 
        /**
         * Organisation party member fields.
         */
        organisation: GetOrganisationCDto | Record<string, never>;
    }

    export interface ExtendedPartyCDto extends PartyCDto
    {
        fields: { [key: string]: string };
        statistics?: { [key: string]: string };
        parentRelationFields?: { [key: string]: string };
        childRelationCount?: number;
        childRelationFields?: { [key: string]: string };
        partyRoles?: PartyRoleCDto[];
        contactFields: ContactFieldDto;
        address_1: PartyAddressCDto;
        address_2: PartyAddressCDto;
        notificationConfig?: {
            [key in Exclude<keyof typeof NotificationTypeEnum, number>]: { 
                [key in Exclude<keyof typeof NotificationChannelEnum, number>]?: NotificationConfigCDto | undefined 
          } | undefined
        }; 
        countryCode: string; //UI Only field
        rootOrganisationDisplayName?: string
    }
    
    export interface NotificationConfigCDto {
      notificationChannelId: number; // 7
      notificationChannelLabel: string; // WhatsApp
      notificationTypeId: number; // 1
      notificationTypeLabel: string; // Global
      isEnabled: boolean
    }
    
    export interface ListPartyCDto extends ExtendedPartyCDto {}

    export interface ContactFieldDto {
        email_1: string;
        phone_1: string;
    }
    export interface PartyAddressCDto {
        lines?: string;
        city?: string;
        stateOrProvince?: string;
        postalCode?: string;
        countryCode?: string;
        countryName?: string;
        fullAddress?: string;
    }
    
    export interface GetForAvatarPartyCDto {
        partyId: string;
        name: string;
        avatarImageUrl?: string;
        atr1Val?: Object;
    }
}