import { But<PERSON> } from "@mui/material";
import { Field, FieldProps, Formik, FormikErrors, FormikHelpers } from "formik";
import { dialog } from "redi-formik-material";
import { BasePartyRelationshipCDto, ExtendedPartyCDto, GetListPartyRelationshipCDto } from "redi-types";
import { useEffect, useRef, useState } from "react";
import { HttpPromise } from "redi-http";
import './styles.scss';
import PartyRelationshipService from "../../../../services/partyrelationship";
import PersonAutocompleteMultiselectField from "../../../../components/PersonAutocompleteMultiselectField/PersonAutocompleteMultiselectField";
import { startOfDay } from "date-fns";
import FormHeader from "../../../../components/Form/Header/FormHeader";

type InitialValues = { partyRoleFromId: string, partyRelationTypeCode: string, members: ExtendedPartyCDto[], originals: Record<string, GetListPartyRelationshipCDto> };

function parsePartyRelationshipList(initialValues: InitialValues) {
    const todayDate = startOfDay(new Date());
    const list: BasePartyRelationshipCDto[] = [];
    const originals = { ...initialValues.originals };
    for (const item of initialValues.members){
        if (originals.hasOwnProperty(item.partyId!)) {
            list.push(initialValues.originals[item.partyId!]);
            delete originals[item.partyId!];
        } else {
            const partyRole = item.partyRoles?.[0];
            if (partyRole) {
                list.push({ 
                    partyRoleFromId: initialValues.partyRoleFromId,
                    partyRoleToId: partyRole?.partyRoleId,
                    partyRelationshipTypeCode: initialValues.partyRelationTypeCode, 
                    inversePartyRelationshipTypeCode: initialValues.partyRelationTypeCode 
                });
            }
        }
    }

    for (const key in originals) {
        const item = initialValues.originals[key];
        list.push({...initialValues.originals[key], toDate: todayDate});
    }
    return list;
}

function MembersForm(props: Props) {

    const { onCancel, onSave, initialValues } = props;
    const [ isSaving, setIsSaving ] = useState(false);
    const promiseRef = useRef<HttpPromise<void>>();

    const save = async (data: InitialValues, actions: FormikHelpers<InitialValues>) => {
        try {
            if (isSaving) { return; }
            setIsSaving(true);
            const list = parsePartyRelationshipList(data);
            promiseRef.current = PartyRelationshipService.bulkUpdateRelationship(list);
            const response = await promiseRef.current;
            if (!response.error) {
                onSave();
            }
        } catch (error) {
        } finally {
            setIsSaving(false);
            actions.setSubmitting(false);
        }
    };
    
    const clearPromises = () => {
        if (promiseRef.current?.cancel) {
            promiseRef.current.cancel();
        }
    };

    useEffect(() => {
        return clearPromises;
    }, []);
    
    const handleClose = () => {
        onCancel();
    };

    const handleViewPartyOnChange = (setFieldValue: (field: string, value: Partial<ExtendedPartyCDto>[], shouldValidate?: boolean) => Promise<void | FormikErrors<InitialValues>>) => (e: React.ChangeEvent<{}>, list: Partial<ExtendedPartyCDto>[], removedItem?: ExtendedPartyCDto) => {
        setFieldValue("members", list);
    };

    return (
        <div>
            <Formik<InitialValues>
                initialValues={initialValues}
                enableReinitialize={true}
                onSubmit={(data, actions) => {
                    dialog({
                        title: "Confirmation",
                        bodyText: "Are you sure you want to save?",
                        agreeText:'OK',
                        disagreeText:'Cancel',
                        onClick: () => {
                            save(data, actions);
                        },
                        onClose: () => {
                            actions.setSubmitting(false);
                        }
                    });
                }}
            >
            { form => { 
                return (
                    <form onSubmit={form.handleSubmit}>
                        <div styleName="form-grid">
                            <FormHeader>Add or Remove Members</FormHeader>
                            <Field name={`members`}>
                                {(fieldProps: FieldProps<GetListPartyRelationshipCDto>) => (
                                    <PersonAutocompleteMultiselectField
                                        id={"teamedit-" + fieldProps.field.name}
                                        name={fieldProps.field.name}
                                        params={{
                                            hasRoleCode: "Employee"
                                        }}
                                        label="Members"
                                        value={form.values.members}
                                        onChange={handleViewPartyOnChange(form.setFieldValue)}
                                    />
                                )}
                            </Field>
                        </div>
                        <div styleName="space-between mg-top-md flex pad-md">
                            <Button variant="outlined" onClick={handleClose}>Cancel</Button>
                            <Button variant="contained" type="submit">Save</Button>
                        </div>
                    </form>
                )
            }}
            </Formik>
        </div>
    );
}

export default MembersForm;

interface Props {
    onCancel: () => void;
    onSave: (dto?: InitialValues) => void;
    initialValues: InitialValues;
}