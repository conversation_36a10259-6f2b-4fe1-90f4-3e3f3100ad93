
import { Typography } from '@mui/material';
import FormHeader from '../../Form/Header/FormHeader';
import './styles.scss';


interface Props {
  title: string;
  bodyText: string;
}

function HeadingInformation(props: Props) {
  const { title, bodyText } = props;
  return (
    <>
      <FormHeader>{title}</FormHeader>
      <Typography variant="body2" component="p">
        {bodyText}
      </Typography>
    </>
  );
}

export default HeadingInformation;