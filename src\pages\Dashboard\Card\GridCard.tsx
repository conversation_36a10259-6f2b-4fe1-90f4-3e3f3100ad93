import { Card, CardContent } from "@mui/material";
import CardFooter from "./Footer/CardFooter";
import <PERSON><PERSON>eader from "./Header/CardHeader";
import WidgetGridCardProvider, { WidgetGridCardState } from "../../../context/WidgetGridCard/WidgetGridCardProvider";
import { useContext, useState } from "react";
import { WidgetGridCardContext } from "../../../context/WidgetGridCard/WidgetGridCardContext";
import CardBody from "./Body/CardBody";
import "./styles.scss";
import ErrorBoundary from "../../Error/ErrorBoundary";
import { type PollingMeta } from "querydatacomponents/useDataPolling";
import { RegisterExportableWidgets } from "../../../context/ExportableWidgetStore/ExportableWidgetStoreContext";

function RenderGridCard() {
    const { widget, editMode, disableResize, isMobile, widget: { title, caption }, isViewing, onSetWidgetEdit, registerWidget, deregisterWidget } = useContext(WidgetGridCardContext)!;
    const [polledWidgetData, setPolledWidgetData] = useState<PollingMeta>();

    return (
        <Card style={{ height: "100%" }}>
            <CardContent styleName="widget-card-content" style={isMobile ? {padding: "3px"} : undefined}>
                <ErrorBoundary>
                    <CardHeader title={title} editMode={editMode} widget={widget} isMobile={isMobile} onSetWidgetEdit={onSetWidgetEdit} polledWidgetData={polledWidgetData}/>
                    <ErrorBoundary>
                        <CardBody 
                            widget={widget} 
                            editMode={editMode} 
                            isViewing={isViewing} 
                            onDataPolled={setPolledWidgetData} 
                            registerWidget={registerWidget} 
                            deregisterWidget={deregisterWidget} 
                        />
                    </ErrorBoundary>
                    <CardFooter caption={caption} editMode={editMode} disableResize={disableResize} />
                </ErrorBoundary>
            </CardContent>   
        </Card>
    );
}

function GridCard(props: Props) {
    return (
        <WidgetGridCardProvider {...props}>
            <RenderGridCard />
        </WidgetGridCardProvider>
    );
}

type Props = WidgetGridCardState & RegisterExportableWidgets;

export default GridCard;