import { Box, Button, FormControlLabel, Grid, MenuItem, Select, SelectChangeEvent, Switch, Typography, useTheme, useMediaQuery, IconButton, Menu, MenuItem as MuiMenuItem } from "@mui/material";
import useDashboardStore from "../../hooks/useDashboardStore";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import FontAwesomeIconButton from "../../../../components/FontAwesomeIconButton/FontAwesomeIconButton";
import { useNavigate } from "react-router-dom";
import { PageContext } from "../../../../enum/pageContextEnum";
import PowerPointButton from "./PowerPointButton/PowerPointButton";
import "./styles.scss";
import {Tooltip} from "@mui/material";
import { useState, useRef, useCallback } from "react";
import Swipe from "react-easy-swipe";

function DashboardHeader() {
    const { dashboard, editMode, fullscreenMode, isSaving, openManager, openSearch, fetchNextDashboard, setOpenSearch, setFullscreenMode, setEditMode, setOpenManager } = useDashboardStore();
    const navigate = useNavigate();
    const theme = useTheme();

    // Mobile detection - using 600px breakpoint (same as project's $xsmall)
    const isMobile = useMediaQuery('(max-width:599px)');

    // Mobile menu state
    const [mobileMenuAnchorEl, setMobileMenuAnchorEl] = useState<null | HTMLElement>(null);
    const isMobileMenuOpen = Boolean(mobileMenuAnchorEl);

    // Swipe container ref
    const swipeContainerRef = useRef<HTMLDivElement>(null);
    const handleEditModeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setEditMode(!editMode);
    };

    const handleAddWidgetClick = () => {
        navigate("/integrations", { state: { context: PageContext.AddDashboardWidget, referenceNo: dashboard.referenceNo, subject: dashboard.subject } });
    };

    const handleFullscreenModeChange = () => {
        setFullscreenMode(!fullscreenMode);
    };

    const handleOpenManagerClick = () => {
        setOpenManager(!openManager);
    };

    const handleOpenSearchClick = () => {
        setOpenSearch(!openSearch);
    };

    const handleFetchPreviousDashboard = () => {
        fetchNextDashboard(dashboard.referenceNo!, false);
    };
    
    const handleFetchNextDashboard = () => {
        fetchNextDashboard(dashboard.referenceNo!);
    };

    // Mobile menu handlers
    const handleMobileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
        setMobileMenuAnchorEl(event.currentTarget);
    };

    const handleMobileMenuClose = () => {
        setMobileMenuAnchorEl(null);
    };

    // Swipe handlers
    const handleSwipeLeft = useCallback(() => {
        if (isMobile) {
            handleFetchNextDashboard();
        }
    }, [isMobile]);

    const handleSwipeRight = useCallback(() => {
        if (isMobile) {
            handleFetchPreviousDashboard();
        }
    }, [isMobile]);

    return (
        <>
            <Swipe
                onSwipeLeft={handleSwipeLeft}
                onSwipeRight={handleSwipeRight}
                tolerance={50}
            >
                <Grid container bgcolor={theme.palette.background.paper} ref={swipeContainerRef}>
                    <Grid item xs={12}>
                        <div styleName="space-between flex pad-md height-md ">
                            <Box display="flex" justifyContent="space-between" alignItems="center">
                                {/* Navigation arrows - hidden on mobile */}
                                <div styleName={`pad-right-sm ${isMobile ? 'mobile-hidden' : ''}`}>
                                    <FontAwesomeIconButton onClick={handleFetchPreviousDashboard} icon={["fad", "arrow-circle-left"]}/>
                                    <Tooltip title="Select another dashboard to view">
                                        <span style={{display:"inline-block"}}>
                                            <FontAwesomeIconButton onClick={handleOpenSearchClick} icon={["fad", "list-tree"]} />
                                        </span>
                                    </Tooltip>
                                    <FontAwesomeIconButton onClick={handleFetchNextDashboard} icon={["fad", "arrow-circle-right"]} />
                                </div>
                                <Typography variant="subtitle1" fontWeight="bold" color={theme.palette.text.primary}>
                                    {dashboard?.subject}
                                </Typography>
                                {/* PowerPoint button - hidden on mobile */}
                                <div styleName={isMobile ? 'mobile-hidden' : ''}>
                                    <Tooltip title="Click to download dashboard as Powerpoint">
                                        <span style={{display:"inline-block"}}>
                                            <PowerPointButton dashboard={dashboard}/>
                                        </span>
                                    </Tooltip>
                                </div>
                            </Box>
                        <div styleName="action-buttons">
                            {/* Desktop action buttons - hidden on mobile */}
                            <div styleName={isMobile ? 'mobile-hidden' : ''}>
                                {!editMode ?
                                <FormControlLabel
                                    labelPlacement="start"
                                    control={<Switch checked={fullscreenMode} onChange={handleFullscreenModeChange} />}
                                    label="Fullscreen"
                                /> : null}
                                {dashboard?.userCanEdit ?
                                <FormControlLabel
                                    labelPlacement="start"
                                    control={<Switch checked={editMode} onChange={handleEditModeChange} />}
                                    label="Edit Mode"
                                /> : null}
                                {editMode ?
                                <>
                                    <Button disabled={isSaving} onClick={handleAddWidgetClick} color="primary" variant="contained">
                                        Add Widget <FontAwesomeIcon icon="plus" />
                                    </Button>
                                    <Button disabled={isSaving} onClick={handleOpenManagerClick} color="primary" variant="contained">
                                        Manage <FontAwesomeIcon icon={["fad", "gears"]}/>
                                    </Button>
                                </> : null}
                            </div>

                            {/* Mobile gear icon - shown only on mobile */}
                            {isMobile && (
                                <IconButton
                                    onClick={handleMobileMenuOpen}
                                    color="primary"
                                    aria-label="settings"
                                >
                                    <FontAwesomeIcon icon={["fad", "gear"]} />
                                </IconButton>
                            )}
                        </div>
                    </div>
                </Grid>
                <Grid item xs={12}>
                    <div styleName="space-between flex pad-md">
                        <div>
                            {dashboard?.config?.isDateRangePickerEnabled ? 
                            <div>{/**TODO: Date Picker Here*/}</div> : null}
                        </div>
                        <div />
                    </div>
                </Grid>
            </Grid>

            {/* Mobile Menu */}
            <Menu
                anchorEl={mobileMenuAnchorEl}
                open={isMobileMenuOpen}
                onClose={handleMobileMenuClose}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                }}
            >
                {/* PowerPoint Button in mobile menu */}
                <MuiMenuItem onClick={handleMobileMenuClose}>
                    <PowerPointButton dashboard={dashboard}/>
                </MuiMenuItem>

                {/* Fullscreen toggle in mobile menu */}
                {!editMode && (
                    <MuiMenuItem>
                        <FormControlLabel
                            labelPlacement="start"
                            control={<Switch checked={fullscreenMode} onChange={handleFullscreenModeChange} />}
                            label="Fullscreen"
                        />
                    </MuiMenuItem>
                )}

                {/* Edit mode toggle in mobile menu */}
                {dashboard?.userCanEdit && (
                    <MuiMenuItem>
                        <FormControlLabel
                            labelPlacement="start"
                            control={<Switch checked={editMode} onChange={handleEditModeChange} />}
                            label="Edit Mode"
                        />
                    </MuiMenuItem>
                )}

                {/* Edit mode buttons in mobile menu */}
                {editMode && (
                    <>
                        <MuiMenuItem onClick={() => { handleAddWidgetClick(); handleMobileMenuClose(); }}>
                            <Button disabled={isSaving} color="primary" variant="contained" fullWidth>
                                Add Widget <FontAwesomeIcon icon="plus" />
                            </Button>
                        </MuiMenuItem>
                        <MuiMenuItem onClick={() => { handleOpenManagerClick(); handleMobileMenuClose(); }}>
                            <Button disabled={isSaving} color="primary" variant="contained" fullWidth>
                                Manage <FontAwesomeIcon icon={["fad", "gears"]}/>
                            </Button>
                        </MuiMenuItem>
                    </>
                )}
            </Menu>
        </Swipe>
        </>
    );
}

export default DashboardHeader;