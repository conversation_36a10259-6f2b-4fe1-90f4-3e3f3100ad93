/*

Add icons here:
    import { faGears } from "@fortawesome/pro-duotone-svg-icons";    
    library.add(faTimes)

Using import:
    import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

Using solid icons:
    <FontAwesomeIcon icon="times" />

Using pro light icons:
    <FontAwesomeIcon icon={['fal', 'gears']} />

Using pro regular icons:
    <FontAwesomeIcon icon={['far', 'gears']} />

Using pro thin icons:
    <FontAwesomeIcon icon={['fat', 'gears']} />

Using pro duotone icons:
    <FontAwesomeIcon icon={['fad', 'gears']} />

Setting duotone styles in CSS:
    --fa-primary-color: red;
    --fa-secondary-color: orange;
    --fa-primary-opacity: 0.8;
    --fa-secondary-opacity: 0.5
 
*/

import { IconDefinition, library } from "@fortawesome/fontawesome-svg-core";
import { faUsersRectangle, faScreenUsers, faIdCardClip, faPeopleGroup, faUser, faCalendar, faExclamation, faBars, faPerson, faQuestionCircle, faCaretDown, faApartment, faArrowRightFromBracket } from "@fortawesome/pro-duotone-svg-icons";
import { faFolder as faFolderRegular } from "@fortawesome/pro-regular-svg-icons";
import { faShieldHalved, faLockAlt, faFileLines, faCheckCircle, faPhone, faEnvelope, faEdit, faRotateRight, faCheck, faChevronLeft, faChevronRight, faCirclePlus, faClose, faEllipsisV, faKey, faLock, faNote, faPaste, faPenToSquare, faScissors, faTrash, faTriangleExclamation, faUserLock, faUserUnlock, faBell } from "@fortawesome/pro-solid-svg-icons";

const exportedIcons: IconDefinition[] = [
    // Add any icons that are used in exported components here (ie. SideMenu)
    faShieldHalved,
    faLockAlt,
    faIdCardClip,
    faCirclePlus,
    faUser,
    faClose,
    faRotateRight,
    faCheck,
    faPerson,
    faEllipsisV,
    faExclamation,
    faPenToSquare,
    faCalendar,
    faEdit,
    faFileLines,
    faCheckCircle,
    faBars,
    faPhone,
    faEnvelope,
    faPeopleGroup,
    faQuestionCircle,
    faCaretDown,
    faApartment,
    faArrowRightFromBracket,
    faBell,
    faUsersRectangle,
    faScreenUsers
];

library.add(
    ...exportedIcons,
    /* Local icons (demo) */
    // Regular
    faFolderRegular,
    // Solid
    faClose,
    faChevronLeft,
    faChevronRight,
    faScissors,
    faPaste,
    faTriangleExclamation,
    faKey,
    faUserLock,
    faUserUnlock,
    faLock,
    faNote,
    faTrash
);

export default exportedIcons; // Export to Container Micro UI