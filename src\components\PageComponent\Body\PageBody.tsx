import { Box, Card, CircularProgress } from "@mui/material";

function PageBody(props: Props) {

    const { isLoading, children } = props;

    return (
        <Card className="page-body">
            {isLoading ?
            <Box
                display="flex"
                height="100%"
                flexDirection="column"
                justifyContent="center"
                alignItems="center"
                textAlign="center"
            >
                <CircularProgress size={50} />...loading
            </Box> : children}
        </Card>
    );
}

export default PageBody;

interface Props {
    children: React.ReactNode;
    isLoading?: boolean;
}