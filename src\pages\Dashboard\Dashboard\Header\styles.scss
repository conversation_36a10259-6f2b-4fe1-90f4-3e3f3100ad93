.space-between {
    justify-content: space-between;
    align-items: center;
}
.flex {
    display:flex;
}
.pad-md {
    padding: 0px 30px;
}
.height-md {
    min-height:77px
}
.pad-right-sm {
    padding-right: 7px;
}
.action-buttons {
    >button>svg {
        margin-left:7px;
    }
    >button:first-child {
        margin-right:25px;
    }
    >button {
        margin-left: 16px;
    }
}

.mobile-hidden {
    @media (max-width: 599px) {
        display: none !important;
    }
}
