declare module "redi-types" {
  export interface BasePartyRelationshipCDto {
    partyRelationshipId?: string;
    partyRelationshipTypeCode: string;
    inversePartyRelationshipTypeCode: string;
    partyRoleToId: string;
    partyRoleFromId: string;
    fromDate?: Date;
    toDate?: Date;
    sortOrder?: number;
  }

  export interface GetPartyRelationshipCDto extends BasePartyRelationshipCDto {
    partyRelationshipTypeLabel: string;
    inversePartyRelationshipTypeLabel: string;
  }

  export interface GetListPartyRelationshipCDto extends BasePartyRelationshipCDto {
    partyRelationshipTypeLabel: string;
    inversePartyRelationshipTypeLabel: string;
    toPartyId: string;
    fromPartyId: string;
    toPartyName: string;
    fromPartyName: string;
    toRoleTypeCode: string;
    fromRoleTypeCode: string;
    toRoleTypeLabel: string;
    fromRoleTypeLabel: string;
    relatedParty?: GetExtendedPartyCDto;
    toPartyAvatarImageUrl?: string;
    fromPartyAvatarImageUrl?: string;
  }

  export interface GetListPartyRelationshipListCDto {
    tempId: string;
    toPartyId: string;
    fromPartyId: string;
    partyRelationshipTypeCodes: string;
    inversePartyRelationshipTypeCodes: string;
    fromDate?: Date;
    partyRelationshipTypeLabels: string;
    inversePartyRelationshipTypeLabels: string;
    toPartyName: string;
    fromPartyName: string;
    partyRelationships: string[];
    inversePartyRelationships: string[];
    relatedParty?: PartyCDto;
  }
}